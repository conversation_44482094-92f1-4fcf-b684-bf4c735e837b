{
    "recommendations": [
        // Essential .NET Development
        "ms-dotnettools.csharp",
        "ms-dotnettools.csdevkit",
        "ms-dotnettools.vscodeintellicode-csharp",
        // XML/XAML Support
        "redhat.vscode-xml",
        "ms-vscode.vscode-xml",
        // Git and Version Control
        "mhutchie.git-graph",
        "eamodio.gitlens",
        // Code Quality and Analysis
        "ms-vscode.vscode-json",
        "editorconfig.editorconfig",
        "streetsidesoftware.code-spell-checker",
        // Productivity
        "ms-vscode.powershell",
        "formulahendry.auto-rename-tag",
        "bradlc.vscode-tailwindcss",
        // Documentation
        "yzhang.markdown-all-in-one",
        "davidanson.vscode-markdownlint",
        // Debugging and Testing
        "ms-vscode.test-adapter-converter",
        "hbenl.vscode-test-explorer"
    ]
}