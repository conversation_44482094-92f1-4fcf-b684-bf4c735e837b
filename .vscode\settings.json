{
    // PowerVoice - VS Code Settings
    "dotnet.defaultSolution": "PowerVoice.sln",
    "omnisharp.enableEditorConfigSupport": true,
    "omnisharp.enableRoslynAnalyzers": true,
    // C# Language Settings
    "csharp.semanticHighlighting.enabled": true,
    "csharp.format.enable": true,
    "csharp.inlayHints.enableInlayHintsForIndexerParameters": true,
    "csharp.inlayHints.enableInlayHintsForLiteralParameters": true,
    "csharp.inlayHints.enableInlayHintsForObjectCreationParameters": true,
    "csharp.inlayHints.enableInlayHintsForOtherParameters": true,
    "csharp.inlayHints.enableInlayHintsForParameters": true,
    "csharp.inlayHints.enableInlayHintsForTypes": true,
    // Editor Settings
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.formatOnType": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "explicit"
    },
    // File Associations
    "files.associations": {
        "*.xaml": "xml",
        "*.ruleset": "xml",
        "*.config": "xml"
    },
    // Exclude Files from Explorer
    "files.exclude": {
        "**/bin": true,
        "**/obj": true,
        "**/.vs": true,
        "**/packages": true,
        "**/*.user": true
    },
    // Search Exclude
    "search.exclude": {
        "**/bin": true,
        "**/obj": true,
        "**/.vs": true,
        "**/packages": true,
        "**/*.user": true
    },
    // IntelliSense and Code Analysis
    "editor.suggest.insertMode": "replace",
    "editor.acceptSuggestionOnCommitCharacter": true,
    "editor.acceptSuggestionOnEnter": "on",
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": true
    },
    // Terminal Settings
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.cwd": "${workspaceFolder}",
    // XAML Formatting (if XAML extension is installed)
    "xml.format.enabled": true,
    "xml.format.legacy": false,
    "xml.format.enforceQuoteStyle": "double",
    "xml.format.preserveAttributeLineBreaks": false,
    "xml.format.splitAttributes": false,
    // Git Settings
    "git.ignoreLimitWarning": true,
    "git.autofetch": true,
    // Extensions Recommendations
    "extensions.ignoreRecommendations": false,
    "dotnet.formatting.organizeImportsOnFormat": true,
    "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true
}