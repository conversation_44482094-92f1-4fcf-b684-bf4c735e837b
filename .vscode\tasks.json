{"version": "2.0.0", "tasks": [{"label": "build-debug", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/PowerVoice.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary", "--configuration", "Debug"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$msCompile"}, {"label": "build-release", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/PowerVoice.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary", "--configuration", "Release"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$msCompile"}, {"label": "build-production", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/PowerVoice.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary", "--configuration", "Production"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$msCompile"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/PowerVoice.sln"], "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": "$msCompile"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/PowerVoice.sln"], "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "publish-debug", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/PowerVoice/PowerVoice.csproj", "--configuration", "Debug", "--output", "${workspaceFolder}/publish/debug"], "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": "$msCompile"}, {"label": "publish-release", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/PowerVoice/PowerVoice.csproj", "--configuration", "Release", "--output", "${workspaceFolder}/publish/release"], "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": "$msCompile"}, {"label": "publish-production", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/PowerVoice/PowerVoice.csproj", "--configuration", "Production", "--output", "${workspaceFolder}/publish/production"], "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": "$msCompile"}, {"label": "run-release", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/PowerVoice/PowerVoice.csproj", "--configuration", "Release"], "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": "$msCompile"}, {"label": "run-production", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/PowerVoice/PowerVoice.csproj", "--configuration", "Production"], "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": "$msCompile"}, {"label": "run-debug", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/PowerVoice/PowerVoice.csproj", "--configuration", "Debug"], "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": "$msCompile"}, {"label": "test", "command": "dotnet", "type": "process", "args": ["test", "${workspaceFolder}/PowerVoice.sln"], "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": "$msCompile"}]}