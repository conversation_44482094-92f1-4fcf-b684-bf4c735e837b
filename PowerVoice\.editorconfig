# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
indent_style = space
insert_final_newline = true
trim_trailing_whitespace = true
charset = utf-8

# C# files
[*.cs]
indent_size = 4

# Code style rules
dotnet_sort_system_directives_first = true
dotnet_separate_import_directive_groups = false

# Unused members - treat as errors
dotnet_diagnostic.IDE0051.severity = error  # Remove unused private members
dotnet_diagnostic.IDE0052.severity = error  # Remove unread private members
dotnet_diagnostic.CS0169.severity = error   # Field is never used
dotnet_diagnostic.CS0414.severity = error   # Field is assigned but its value is never used
dotnet_diagnostic.CA1823.severity = error   # Avoid unused private fields
dotnet_diagnostic.CA1812.severity = error   # Avoid uninstantiated internal classes

# Additional code quality rules
dotnet_diagnostic.CA1822.severity = suggestion  # Mark members as static
dotnet_diagnostic.IDE0059.severity = warning    # Unnecessary assignment of a value
dotnet_diagnostic.IDE0060.severity = suggestion # Remove unused parameter

# Naming conventions
dotnet_naming_rule.interfaces_should_be_prefixed_with_i.severity = error
dotnet_naming_rule.interfaces_should_be_prefixed_with_i.symbols = interface
dotnet_naming_rule.interfaces_should_be_prefixed_with_i.style = prefixed_with_i

dotnet_naming_rule.types_should_be_pascal_case.severity = error
dotnet_naming_rule.types_should_be_pascal_case.symbols = types
dotnet_naming_rule.types_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.non_field_members_should_be_pascal_case.severity = error
dotnet_naming_rule.non_field_members_should_be_pascal_case.symbols = non_field_members
dotnet_naming_rule.non_field_members_should_be_pascal_case.style = pascal_case

# Symbol specifications
dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected

dotnet_naming_symbols.types.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.types.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected

dotnet_naming_symbols.non_field_members.applicable_kinds = property, event, method
dotnet_naming_symbols.non_field_members.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected

# Naming styles
dotnet_naming_style.prefixed_with_i.required_prefix = I
dotnet_naming_style.prefixed_with_i.capitalization = pascal_case

dotnet_naming_style.pascal_case.capitalization = pascal_case

# XAML files
[*.xaml]
indent_size = 2

# XML files
[*.xml]
indent_size = 2

# JSON files
[*.json]
indent_size = 2

# PowerShell files
[*.ps1]
indent_size = 4

# Markdown files
[*.md]
trim_trailing_whitespace = false