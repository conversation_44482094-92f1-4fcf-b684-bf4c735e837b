﻿using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Windows;

namespace PowerVoice;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // DISABLE HARDWARE ACCELERATION: Eliminates Intel graphics driver bloat (110MB!)
        System.Windows.Media.RenderOptions.ProcessRenderMode = System.Windows.Interop.RenderMode.SoftwareOnly;

        // Configuration-specific startup logic
        HandleProductionMode();

        // Add assembly resolver for OpenAI and other dependencies
        AppDomain.CurrentDomain.AssemblyResolve += CurrentDomain_AssemblyResolve;

        base.OnStartup(e);
    }

    /// <summary>
    /// Handles production mode specific configuration
    /// </summary>
    private void HandleProductionMode()
    {
#if PRODUCTION
        // Disable debugger attachment in production mode
        if (Debugger.IsAttached)
        {
            System.Windows.MessageBox.Show("This application cannot run with a debugger attached in production mode.",
                           "Production Mode", MessageBoxButton.OK, MessageBoxImage.Warning);
            Shutdown(-1);
            return;
        }

        // Disable debug trace output
        System.Diagnostics.Trace.Listeners.Clear();

        // Set process priority to high for better performance
        Process.GetCurrentProcess().PriorityClass = ProcessPriorityClass.High;
#endif
    }

    /// <summary>
    /// Assembly resolver to help locate OpenAI and other dependencies
    /// that might not be properly resolved in .NET 9 applications.
    /// </summary>
    private static Assembly? CurrentDomain_AssemblyResolve(object? sender, ResolveEventArgs args)
    {
        try
        {
            var assemblyName = new AssemblyName(args.Name);
            var simpleName = assemblyName.Name;

            if (string.IsNullOrEmpty(simpleName))
                return null;

            var appDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            if (string.IsNullOrEmpty(appDirectory))
                return null;

            return TryLoadFromStandardPaths(simpleName, appDirectory) ??
                   TryLoadFromRuntimePaths(simpleName, appDirectory) ??
                   TryLoadOpenAIAssembly(simpleName, appDirectory);
        }
        catch
        {
            return null;
        }
    }

    private static Assembly? TryLoadFromStandardPaths(string simpleName, string appDirectory)
    {
        var standardPaths = new[]
        {
            Path.Combine(appDirectory, $"{simpleName}.dll"),
            Path.Combine(appDirectory, $"{simpleName}.exe")
        };

        return standardPaths
            .Where(File.Exists)
            .Select(TryLoadAssembly)
            .FirstOrDefault(assembly => assembly != null);
    }

    private static Assembly? TryLoadFromRuntimePaths(string simpleName, string appDirectory)
    {
        var runtimePaths = new[]
        {
            Path.Combine(appDirectory, "runtimes", "win", "lib", "net9.0", $"{simpleName}.dll"),
            Path.Combine(appDirectory, "runtimes", "win-x64", "lib", "net9.0", $"{simpleName}.dll"),
            Path.Combine(appDirectory, "runtimes", "any", "lib", "net9.0", $"{simpleName}.dll")
        };

        return runtimePaths
            .Where(File.Exists)
            .Select(TryLoadAssembly)
            .FirstOrDefault(assembly => assembly != null);
    }

    private static Assembly? TryLoadOpenAIAssembly(string simpleName, string appDirectory)
    {
        if (!simpleName.Contains("OpenAI"))
            return null;

        try
        {
            var searchDirs = Directory.GetDirectories(appDirectory, "*", SearchOption.AllDirectories);
            return searchDirs
                .Select(dir => Path.Combine(dir, $"{simpleName}.dll"))
                .Where(File.Exists)
                .Select(TryLoadAssembly)
                .FirstOrDefault(assembly => assembly != null);
        }
        catch
        {
            return null;
        }
    }

    private static Assembly? TryLoadAssembly(string path)
    {
        try
        {
            var assemblyName = AssemblyName.GetAssemblyName(path);
            return Assembly.Load(assemblyName);
        }
        catch
        {
            return null;
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        // Remove the assembly resolver
        AppDomain.CurrentDomain.AssemblyResolve -= CurrentDomain_AssemblyResolve;

        base.OnExit(e);
    }
}

