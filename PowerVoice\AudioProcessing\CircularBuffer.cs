namespace PowerVoice.AudioProcessing
{
    /// <summary>
    /// High-performance circular buffer for audio processing
    /// O(1) operations for Add, Clear, and property access
    /// </summary>
    public class CircularBuffer<T>
    {
        private readonly T[] _buffer;
        private readonly int _capacity;
        private int _head = 0;
        private int _count = 0;

        public CircularBuffer(int capacity)
        {
            if (capacity <= 0)
                throw new ArgumentException("Capacity must be positive", nameof(capacity));

            _capacity = capacity;
            _buffer = new T[capacity];
        }

        /// <summary>
        /// Add item to buffer - O(1) operation
        /// </summary>
        public void Add(T item)
        {
            _buffer[_head] = item;
            _head = (_head + 1) % _capacity;
            if (_count < _capacity) _count++;
        }

        /// <summary>
        /// Peek at oldest item without removing - O(1) operation
        /// </summary>
        public T Peek()
        {
            if (_count == 0)
                throw new InvalidOperationException("Buffer is empty");

            int tail = (_head - _count + _capacity) % _capacity;
            return _buffer[tail];
        }

        /// <summary>
        /// Clear all items - O(1) operation
        /// </summary>
        public void Clear()
        {
            _count = 0;
            _head = 0;
        }

        /// <summary>
        /// Check if buffer is full - O(1) operation
        /// </summary>
        public bool IsFull => _count == _capacity;

        /// <summary>
        /// Get current count - O(1) operation
        /// </summary>
        public int Count => _count;

        /// <summary>
        /// Get capacity - O(1) operation
        /// </summary>
        public int Capacity => _capacity;
    }
}
