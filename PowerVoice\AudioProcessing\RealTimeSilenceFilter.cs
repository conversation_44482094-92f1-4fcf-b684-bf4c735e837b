using System.Diagnostics;

namespace PowerVoice.AudioProcessing
{
    /// <summary>
    /// Real-time silence filter that processes audio chunks as they arrive from microphone
    /// Drops silence >1 second immediately to prevent wasteful API spending
    /// Also detects longer silences suitable for sentence boundaries in chunking
    /// Has a bypass period for the first second to capture all audio without filtering
    /// </summary>
    public class RealTimeSilenceFilter
    {
        private readonly double _silenceThreshold;
        private readonly int _maxSilenceDurationMs;
        private readonly int _sampleRate;

        // State tracking - O(1) operations
        private bool _isInSilence = false;
        private int _consecutiveSilenceMs = 0;
        private int _droppedSilenceMs = 0;

        // Bypass filtering for first second to capture all audio including first words
        private readonly DateTime _filterStartTime;
        private readonly int _bypassPeriodMs = 1000; // First 1 second - no filtering

        // Chunking support - track longer silences for sentence boundaries
        private readonly int _sentenceBoundaryThresholdMs;

        public RealTimeSilenceFilter(double silenceThreshold = 200.0, int maxSilenceDurationMs = 800, int sampleRate = 16000, int sentenceBoundaryThresholdMs = 1200)
        {
            _silenceThreshold = silenceThreshold;
            _maxSilenceDurationMs = maxSilenceDurationMs;
            _sampleRate = sampleRate;
            _sentenceBoundaryThresholdMs = sentenceBoundaryThresholdMs;
            _filterStartTime = DateTime.UtcNow; // Record when filtering starts for bypass period
        }

        /// <summary>
        /// Real-time filtering - processes each audio chunk as it arrives
        /// O(n) where n = chunk size (typically 100ms of audio)
        /// </summary>
        public FilterResult ProcessAudioChunk(byte[] chunk, int bytesRead)
        {
            var result = new FilterResult();
            var outputBuffer = new List<byte>(bytesRead);

            // Calculate chunk duration for proper timing
            double chunkDurationMs = (bytesRead / 2.0) * GetSampleDurationMs(); // bytesRead/2 = number of samples

            // BYPASS PERIOD: For the first 1 second, capture ALL audio without any filtering
            // This ensures first words are never lost due to aggressive silence detection
            double elapsedMs = (DateTime.UtcNow - _filterStartTime).TotalMilliseconds;
            if (elapsedMs < _bypassPeriodMs)
            {
                // First second - accept everything without filtering
                for (int i = 0; i < bytesRead; i++)
                {
                    outputBuffer.Add(chunk[i]);
                }
                result.FilteredAudio = outputBuffer.ToArray();
                Debug.WriteLine($"🚀 BYPASS: Captured {chunkDurationMs:F0}ms audio without filtering (elapsed: {elapsedMs:F0}ms < {_bypassPeriodMs}ms)");
                return result;
            }

            // Analyze entire chunk for silence
            bool isChunkSilent = IsChunkSilent(chunk, bytesRead);

            if (isChunkSilent)
            {
                if (!_isInSilence)
                {
                    _isInSilence = true;
                    _consecutiveSilenceMs = 0;
                    Debug.WriteLine($"🔇 Silence started - beginning 1-second retention period");
                }

                // Calculate how much of this chunk we should keep to reach exactly 1000ms
                int silenceAfterThisChunk = _consecutiveSilenceMs + (int)chunkDurationMs;

                if (_consecutiveSilenceMs < _maxSilenceDurationMs)
                {
                    // We're still within the 1-second retention period
                    if (silenceAfterThisChunk <= _maxSilenceDurationMs)
                    {
                        // Keep entire chunk - still within 1000ms limit
                        for (int i = 0; i < bytesRead; i++)
                        {
                            outputBuffer.Add(chunk[i]);
                        }
                        Debug.WriteLine($"🔇 Keeping entire {chunkDurationMs:F0}ms silent chunk (total silence: {silenceAfterThisChunk}ms <= 1000ms)");
                    }
                    else
                    {
                        // Partial keep - this chunk would exceed 1000ms
                        int msToKeep = _maxSilenceDurationMs - _consecutiveSilenceMs;
                        int bytesToKeep = (int)((msToKeep / chunkDurationMs) * bytesRead);

                        // Keep only the portion that fits within 1000ms
                        for (int i = 0; i < bytesToKeep; i++)
                        {
                            outputBuffer.Add(chunk[i]);
                        }

                        // Drop the rest
                        int droppedBytes = bytesRead - bytesToKeep;
                        double droppedMs = (droppedBytes / 2.0) * GetSampleDurationMs();
                        _droppedSilenceMs += (int)droppedMs;
                        result.DroppedBytes += droppedBytes;

                        Debug.WriteLine($"🔇 Partial chunk: Kept {msToKeep}ms, dropped {droppedMs:F0}ms (reached 1000ms limit)");
                    }
                }
                else
                {
                    // Already exceeded 1000ms - drop entire chunk
                    _droppedSilenceMs += (int)chunkDurationMs;
                    result.DroppedBytes += bytesRead;
                    Debug.WriteLine($"🔇 Dropped entire {chunkDurationMs:F0}ms silent chunk (total dropped: {_droppedSilenceMs}ms)");
                }

                _consecutiveSilenceMs += (int)chunkDurationMs;

                // Check for sentence boundary silence (longer silence indicating sentence end)
                if (_consecutiveSilenceMs >= _sentenceBoundaryThresholdMs && !result.SentenceBoundaryDetected)
                {
                    result.SentenceBoundaryDetected = true;
                    Debug.WriteLine($"📝 Sentence boundary detected after {_consecutiveSilenceMs}ms silence");
                }
            }
            else
            {
                // Voice detected - always include entire chunk
                for (int i = 0; i < bytesRead; i++)
                {
                    outputBuffer.Add(chunk[i]);
                }

                // Reset silence state when voice is detected
                if (_isInSilence)
                {
                    _isInSilence = false;
                    Debug.WriteLine($"🗣️ Voice detected after {_consecutiveSilenceMs}ms silence, reset counter");
                    _consecutiveSilenceMs = 0;
                }
            }

            result.FilteredAudio = outputBuffer.ToArray();
            result.OriginalSize = bytesRead;
            result.FilteredSize = outputBuffer.Count;
            result.TotalDroppedSilenceMs = _droppedSilenceMs;

            return result;
        }

        /// <summary>
        /// Determine if an entire audio chunk is silent
        /// </summary>
        private bool IsChunkSilent(byte[] chunk, int bytesRead)
        {
            int silentSamples = 0;
            int totalSamples = bytesRead / 2; // 16-bit samples

            for (int i = 0; i < bytesRead; i += 2)
            {
                if (i + 1 < bytesRead)
                {
                    short sample = BitConverter.ToInt16(chunk, i);
                    if (Math.Abs(sample) < _silenceThreshold)
                    {
                        silentSamples++;
                    }
                }
            }

            // Consider chunk silent if >95% of samples are below threshold (was 90%)
            // More conservative to prevent cutting off speech onset
            double silentRatio = (double)silentSamples / totalSamples;
            return silentRatio > 0.95;
        }

        // O(1) operations
        private double GetSampleDurationMs() => 1000.0 / _sampleRate; // ~0.0625ms at 16kHz

        public void Reset()
        {
            _isInSilence = false;
            _consecutiveSilenceMs = 0;
            _droppedSilenceMs = 0;
        }

        public int GetTotalDroppedSilenceMs() => _droppedSilenceMs;

        /// <summary>
        /// Check if we are currently experiencing a sentence boundary silence
        /// Used for intelligent chunking decisions
        /// </summary>
        public bool IsAtSentenceBoundary() => _isInSilence && _consecutiveSilenceMs >= _sentenceBoundaryThresholdMs;

        /// <summary>
        /// Get current consecutive silence duration in milliseconds
        /// </summary>
        public int GetCurrentSilenceDurationMs() => _consecutiveSilenceMs;
    }

    /// <summary>
    /// Result of real-time silence filtering
    /// </summary>
    public class FilterResult
    {
        public byte[] FilteredAudio { get; set; } = Array.Empty<byte>();
        public int OriginalSize { get; set; }
        public int FilteredSize { get; set; }
        public int DroppedBytes { get; set; }
        public int TotalDroppedSilenceMs { get; set; }
        public bool SentenceBoundaryDetected { get; set; } = false;
        public bool WasFiltered => DroppedBytes > 0;
        public double CompressionRatio => OriginalSize > 0 ? (double)FilteredSize / OriginalSize : 1.0;
    }
}
