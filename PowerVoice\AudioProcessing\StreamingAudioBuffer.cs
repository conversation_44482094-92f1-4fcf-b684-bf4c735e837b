using System.Diagnostics;
using System.IO;
using NAudio.Wave;

namespace PowerVoice.AudioProcessing
{
    /// <summary>
    /// Streaming audio buffer that applies real-time silence filtering
    /// Replaces batch-style audio capture with zero-latency filtering
    /// </summary>
    public class StreamingAudioBuffer : IDisposable
    {
        private readonly RealTimeSilenceFilter _silenceFilter;
        private readonly WaveFormat _format;
        private readonly MemoryStream _pcmBuffer; // accumulate raw PCM
        private readonly object _lockObject = new object();
        private bool _disposed = false;

        public StreamingAudioBuffer(WaveFormat format, double silenceThreshold = 200.0, int maxSilenceDurationMs = 800)
        {
            _format = format;
            _silenceFilter = new RealTimeSilenceFilter(silenceThreshold, maxSilenceDurationMs, format.SampleRate);
            // DYNAMIC BUFFER: Start small but allow growth for 30-minute recordings
            // Initial: 32KB, can grow to ~60MB for 30min of raw audio (much less after silence filtering)
            _pcmBuffer = new MemoryStream(capacity: 32 * 1024);
        }

        /// <summary>
        /// Called by NAudio for each audio chunk - O(1) amortized
        /// Real-time silence filtering - no delay, no buffering
        /// </summary>
        public void OnAudioDataAvailable(object? sender, WaveInEventArgs e)
        {
            if (_disposed || e.BytesRecorded == 0) return;

            lock (_lockObject)
            {
                try
                {
                    // Real-time silence filtering - no delay, no buffering
                    var filterResult = _silenceFilter.ProcessAudioChunk(e.Buffer, e.BytesRecorded);

                    // Only write non-silent audio to raw PCM buffer
                    if (filterResult.FilteredAudio.Length > 0)
                    {
                        _pcmBuffer.Write(filterResult.FilteredAudio, 0, filterResult.FilteredAudio.Length);
                    }

                    // Optional: Log cost savings in real-time
                    if (filterResult.DroppedBytes > 0)
                    {
                        Debug.WriteLine($"💰 Saved {filterResult.DroppedBytes} bytes of silence (Total saved: {filterResult.TotalDroppedSilenceMs}ms)");
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ Error in StreamingAudioBuffer: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Get the accumulated filtered audio as a finalized WAV - O(n)
        /// </summary>
        public byte[] GetFilteredAudio()
        {
            lock (_lockObject)
            {
                if (_disposed) return Array.Empty<byte>();

                try
                {
                    var pcm = _pcmBuffer.ToArray();
                    if (pcm.Length == 0)
                    {
                        return Array.Empty<byte>();
                    }

                    using var wavStream = new MemoryStream(capacity: pcm.Length + 44);
                    using (var writer = new WaveFileWriter(wavStream, _format))
                    {
                        writer.Write(pcm, 0, pcm.Length);
                    } // disposing writer finalizes headers
                    var filteredData = wavStream.ToArray();

                    // Log filtering statistics
                    int totalDroppedMs = _silenceFilter.GetTotalDroppedSilenceMs();
                    if (totalDroppedMs > 0)
                    {
                        Debug.WriteLine($"🔇 SILENCE FILTERING STATS: Dropped {totalDroppedMs}ms of silence, Final audio: {filteredData.Length / 1024.0:F1} KB");
                    }
                    else
                    {
                        Debug.WriteLine($"⚠️ NO SILENCE DROPPED - Final audio: {filteredData.Length / 1024.0:F1} KB");
                    }

                    return filteredData;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ Error getting filtered audio: {ex.Message}");
                    return Array.Empty<byte>();
                }
            }
        }

        /// <summary>
        /// Reset for next recording session - O(1)
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                if (_disposed) return;

                try
                {
                    _pcmBuffer.SetLength(0);
                    _silenceFilter.Reset();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ Error resetting StreamingAudioBuffer: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Get total silence dropped in milliseconds
        /// </summary>
        public int GetTotalSilenceDroppedMs() => _silenceFilter.GetTotalDroppedSilenceMs();

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                lock (_lockObject)
                {
                    try
                    {
                        _pcmBuffer?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"❌ Error disposing StreamingAudioBuffer: {ex.Message}");
                    }
                }
                _disposed = true;
            }
        }
    }
}
