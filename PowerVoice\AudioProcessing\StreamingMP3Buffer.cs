using System.Diagnostics;
using System.IO;
using NAudio.Wave;
using PowerVoice.AudioProcessing;

namespace PowerVoice.AudioProcessing
{
    /// <summary>
    /// Lightweight audio buffer replacement for StreamingMP3Buffer
    /// Uses WAV format instead of MP3 to eliminate NAudio.Lame dependency
    /// Provides same interface for compatibility while achieving sub-10MB goal
    /// </summary>
    public class StreamingWavBuffer : IDisposable
    {
        private readonly RealTimeSilenceFilter _silenceFilter;
        private readonly WaveFormat _format;
        private readonly MemoryStream _wavBuffer;
        private readonly object _lockObject = new object();
        private bool _disposed = false;
        private bool _isAtSentenceBoundary = false;

        // Memory leak prevention: Maximum buffer size (10MB for ~5 minutes of audio)
        private const int MAX_BUFFER_SIZE = 10 * 1024 * 1024;

        public StreamingWavBuffer(WaveFormat format, double silenceThreshold = 200.0, int maxSilenceDurationMs = 800, int initialDelayMs = 1200)
        {
            _format = format;
            _silenceFilter = new RealTimeSilenceFilter(silenceThreshold, maxSilenceDurationMs, format.SampleRate);
            // Compact initial buffer - WAV header + 8KB audio data
            _wavBuffer = new MemoryStream(capacity: 8 * 1024 + 44); // WAV header is 44 bytes
            WriteWavHeader();
        }

        /// <summary>
        /// Real-time audio data handler - compatible with NAudio events
        /// </summary>
        public void OnAudioDataAvailable(object? sender, WaveInEventArgs e)
        {
            if (_disposed || e.BytesRecorded == 0) return;

            lock (_lockObject)
            {
                try
                {
                    // Apply real-time silence filtering
                    var filterResult = _silenceFilter.ProcessAudioChunk(e.Buffer, e.BytesRecorded);

                    // Write filtered audio to WAV buffer with size limit to prevent memory leaks
                    if (filterResult.FilteredAudio.Length > 0)
                    {
                        // Prevent buffer from growing beyond reasonable limits
                        if (_wavBuffer.Length + filterResult.FilteredAudio.Length > MAX_BUFFER_SIZE)
                        {
                            Debug.WriteLine($"⚠️ Audio buffer size limit reached ({MAX_BUFFER_SIZE / (1024 * 1024)}MB), starting new chunk");
                            StartNewChunk();
                        }
                        _wavBuffer.Write(filterResult.FilteredAudio, 0, filterResult.FilteredAudio.Length);
                    }

                    // Detect sentence boundaries based on silence patterns
                    _isAtSentenceBoundary = filterResult.DroppedBytes > (_format.AverageBytesPerSecond / 2); // 500ms silence
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"StreamingWavBuffer.OnAudioDataAvailable error: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Check if we're at a natural sentence boundary
        /// </summary>
        public bool IsAtSentenceBoundary()
        {
            lock (_lockObject)
            {
                return _isAtSentenceBoundary;
            }
        }

        /// <summary>
        /// Get current audio chunk as WAV data for transcription
        /// </summary>
        public byte[] GetCurrentChunkWavAudio()
        {
            lock (_lockObject)
            {
                if (_wavBuffer.Length <= 44) return Array.Empty<byte>(); // Only header, no audio

                var audioData = _wavBuffer.ToArray();
                UpdateWavHeader(audioData, (int)(_wavBuffer.Length - 44));
                return audioData;
            }
        }

        /// <summary>
        /// Get all filtered audio as final WAV data
        /// </summary>
        public byte[] GetFilteredWavAudio()
        {
            lock (_lockObject)
            {
                if (_wavBuffer.Length <= 44) return Array.Empty<byte>(); // Only header, no audio

                var audioData = _wavBuffer.ToArray();
                UpdateWavHeader(audioData, (int)(_wavBuffer.Length - 44));
                return audioData;
            }
        }

        /// <summary>
        /// Start a new audio chunk (reset buffer but keep format)
        /// </summary>
        public void StartNewChunk()
        {
            lock (_lockObject)
            {
                _wavBuffer.SetLength(0);
                WriteWavHeader();
                _isAtSentenceBoundary = false;
            }
        }

        /// <summary>
        /// Reset the entire buffer
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _wavBuffer.SetLength(0);
                WriteWavHeader();
                _isAtSentenceBoundary = false;
                _silenceFilter?.Reset();
            }
        }

        /// <summary>
        /// Write WAV file header (44 bytes)
        /// </summary>
        private void WriteWavHeader()
        {
            var header = new byte[44];
            var sampleRate = _format.SampleRate;
            var channels = _format.Channels;
            var bitsPerSample = _format.BitsPerSample;
            var bytesPerSample = bitsPerSample / 8;
            var blockAlign = channels * bytesPerSample;
            var avgBytesPerSec = sampleRate * blockAlign;

            // RIFF header
            System.Text.Encoding.ASCII.GetBytes("RIFF").CopyTo(header, 0);
            BitConverter.GetBytes(36).CopyTo(header, 4); // File size - 8 (will update later)
            System.Text.Encoding.ASCII.GetBytes("WAVE").CopyTo(header, 8);

            // fmt chunk
            System.Text.Encoding.ASCII.GetBytes("fmt ").CopyTo(header, 12);
            BitConverter.GetBytes(16).CopyTo(header, 16); // fmt chunk size
            BitConverter.GetBytes((short)1).CopyTo(header, 20); // PCM format
            BitConverter.GetBytes((short)channels).CopyTo(header, 22);
            BitConverter.GetBytes(sampleRate).CopyTo(header, 24);
            BitConverter.GetBytes(avgBytesPerSec).CopyTo(header, 28);
            BitConverter.GetBytes((short)blockAlign).CopyTo(header, 32);
            BitConverter.GetBytes((short)bitsPerSample).CopyTo(header, 34);

            // data chunk header
            System.Text.Encoding.ASCII.GetBytes("data").CopyTo(header, 36);
            BitConverter.GetBytes(0).CopyTo(header, 40); // Data size (will update later)

            _wavBuffer.Write(header, 0, header.Length);
        }

        /// <summary>
        /// Update WAV header with correct file and data sizes
        /// </summary>
        private static void UpdateWavHeader(byte[] wavData, int audioDataSize)
        {
            if (wavData.Length < 44) return;

            // Update file size (total - 8)
            BitConverter.GetBytes(audioDataSize + 36).CopyTo(wavData, 4);
            // Update data chunk size
            BitConverter.GetBytes(audioDataSize).CopyTo(wavData, 40);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                lock (_lockObject)
                {
                    _wavBuffer?.Dispose();
                    // Note: RealTimeSilenceFilter doesn't implement IDisposable
                    _disposed = true;
                }
            }
        }
    }

    /// <summary>
    /// Alias for backward compatibility with existing code
    /// Maps StreamingMP3Buffer to StreamingWavBuffer
    /// </summary>
    public class StreamingMP3Buffer : StreamingWavBuffer
    {
        public StreamingMP3Buffer(WaveFormat format, double silenceThreshold = 200.0, int maxSilenceDurationMs = 800, int initialDelayMs = 1200)
            : base(format, silenceThreshold, maxSilenceDurationMs, initialDelayMs)
        {
        }

        /// <summary>
        /// Get MP3 audio - returns WAV for compatibility (transcription services accept both)
        /// </summary>
        public byte[] GetCurrentChunkMP3Audio() => GetCurrentChunkWavAudio();

        /// <summary>
        /// Get filtered MP3 - returns WAV for compatibility
        /// </summary>
        public byte[] GetFilteredMP3Audio() => GetFilteredWavAudio();
    }
}
