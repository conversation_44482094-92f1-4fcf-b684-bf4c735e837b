using System;
using System.IO;
using NAudio.Wave;

namespace PowerVoice.AudioProcessing
{
    /// <summary>
    /// Ultra-lightweight PCM audio buffer with silence filtering.
    /// Eliminates NAudio.Lame dependency (1.7MB) using only basic WAV processing.
    /// </summary>
    public class StreamingPcmBuffer : IDisposable
    {
        private readonly RealTimeSilenceFilter _silenceFilter;
        private readonly WaveFormat _format;
        private MemoryStream? _pcmBuffer;
        private readonly object _lockObject = new object();
        private bool _disposed = false;

        public StreamingPcmBuffer(WaveFormat format, double silenceThreshold = 500.0, int maxSilenceDurationMs = 1000, int sentenceBoundaryThresholdMs = 1500)
        {
            _format = format;
            _silenceFilter = new RealTimeSilenceFilter(silenceThreshold, maxSilenceDurationMs, format.SampleRate, sentenceBoundaryThresholdMs);
            _pcmBuffer = new MemoryStream();
        }

        public void OnAudioDataAvailable(object? sender, WaveInEventArgs e)
        {
            if (_disposed || e.BytesRecorded == 0) return;

            lock (_lockObject)
            {
                try
                {
                    // Apply silence filter
                    var filterResult = _silenceFilter.ProcessAudioChunk(e.Buffer, e.BytesRecorded);

                    if (filterResult.FilteredAudio != null && filterResult.FilteredAudio.Length > 0)
                    {
                        _pcmBuffer?.Write(filterResult.FilteredAudio, 0, filterResult.FilteredAudio.Length);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in PCM buffer: {ex.Message}");
                }
            }
        }

        public byte[] GetCurrentChunkPcmAudio()
        {
            lock (_lockObject)
            {
                if (_pcmBuffer == null || _pcmBuffer.Length == 0)
                    return Array.Empty<byte>();

                return _pcmBuffer.ToArray();
            }
        }

        public byte[] GetFilteredPcmAudio()
        {
            lock (_lockObject)
            {
                if (_pcmBuffer == null || _pcmBuffer.Length == 0)
                    return Array.Empty<byte>();

                return _pcmBuffer.ToArray();
            }
        }

        public bool IsAtSentenceBoundary()
        {
            return _silenceFilter.IsAtSentenceBoundary();
        }

        public void StartNewChunk()
        {
            lock (_lockObject)
            {
                _pcmBuffer?.SetLength(0);
            }
        }

        public void Reset()
        {
            lock (_lockObject)
            {
                _pcmBuffer?.SetLength(0);
                _silenceFilter.Reset();
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                lock (_lockObject)
                {
                    _pcmBuffer?.Dispose();
                    _pcmBuffer = null;
                    _disposed = true;
                }
            }
        }
    }
}
