using System;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using PowerVoice.Extensions;

namespace PowerVoice.Configuration
{
    /// <summary>
    /// Manages application configuration including hotkey settings
    /// </summary>
    public class ConfigurationManager
    {
        private readonly string _configFilePath;
        private AppConfiguration? _currentConfig;

        public ConfigurationManager(string configFilePath)
        {
            _configFilePath = configFilePath;
        }

        /// <summary>
        /// Loads the configuration from file
        /// </summary>
        public async Task<AppConfiguration> LoadConfigurationAsync()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    DebugT.WriteLine("⚠️ Configuration file not found, creating default configuration");
                    _currentConfig = CreateDefaultConfiguration();
                    await SaveConfigurationAsync(_currentConfig);
                    return _currentConfig;
                }

                var configJson = await File.ReadAllTextAsync(_configFilePath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                };

                var config = JsonSerializer.Deserialize<AppConfiguration>(configJson, options);
                
                if (config == null)
                {
                    DebugT.WriteLine("⚠️ Failed to deserialize configuration, using default");
                    _currentConfig = CreateDefaultConfiguration();
                }
                else
                {
                    _currentConfig = config;
                    
                    // Ensure hotkey configuration exists (migration for existing installations)
                    if (_currentConfig.Hotkey == null)
                    {
                        DebugT.WriteLine("🔄 Migrating configuration: Adding default hotkey settings");
                        _currentConfig.Hotkey = HotkeyConfig.CreateDefault();
                        await SaveConfigurationAsync(_currentConfig);
                    }
                }

                DebugT.WriteLine($"✅ Configuration loaded successfully. Hotkey: {_currentConfig.Hotkey?.GetDisplayString() ?? "Default"}");
                return _currentConfig;
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Error loading configuration: {ex.Message}");
                _currentConfig = CreateDefaultConfiguration();
                return _currentConfig;
            }
        }

        /// <summary>
        /// Saves the configuration to file
        /// </summary>
        public async Task SaveConfigurationAsync(AppConfiguration config)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                };

                var configJson = JsonSerializer.Serialize(config, options);
                await File.WriteAllTextAsync(_configFilePath, configJson);
                
                _currentConfig = config;
                DebugT.WriteLine($"✅ Configuration saved successfully. Hotkey: {config.Hotkey?.GetDisplayString() ?? "Default"}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Error saving configuration: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Updates only the hotkey configuration
        /// </summary>
        public async Task UpdateHotkeyConfigAsync(HotkeyConfig hotkeyConfig)
        {
            if (_currentConfig == null)
            {
                await LoadConfigurationAsync();
            }

            if (_currentConfig != null)
            {
                _currentConfig.Hotkey = hotkeyConfig;
                await SaveConfigurationAsync(_currentConfig);
            }
        }

        /// <summary>
        /// Gets the current hotkey configuration
        /// </summary>
        public HotkeyConfig GetHotkeyConfig()
        {
            return _currentConfig?.Hotkey ?? HotkeyConfig.CreateDefault();
        }

        /// <summary>
        /// Creates a default configuration
        /// </summary>
        private static AppConfiguration CreateDefaultConfiguration()
        {
            return new AppConfiguration
            {
                AzureSpeech = new AzureSpeechConfig
                {
                    SubscriptionKey = "b47fc8cfc70643e2ab681b48cba6be14",
                    Region = "centralindia",
                    TranscriptionMode = "FastTranscription",
                    FastTranscription = new FastTranscriptionConfig
                    {
                        UseAzure = true,
                        UseWhisper = false,
                        ApiVersion = "2024-05-15-preview",
                        Endpoint = "https://centralindia.api.cognitive.microsoft.com/speechtotext/transcriptions:transcribe",
                        TimeoutSeconds = 60,
                        MaxRetries = 3,
                        OptimizeJsonParsing = true,
                        SkipDetailedWordInfo = true,
                        ChunkOnSilenceDurationMs = 1500,
                        MinChunkDurationMs = 5000,
                        MaxChunkDurationMs = 1800000
                    }
                },
                Hotkey = HotkeyConfig.CreateDefault(),
                Debug = new DebugConfig
                {
                    EnableKeyboardHookLogging = false,
                    EnablePerformanceLogging = false,
                    EnableAnimationSuspension = true
                }
            };
        }
    }

    /// <summary>
    /// Root configuration class
    /// </summary>
    public class AppConfiguration
    {
        public AzureSpeechConfig? AzureSpeech { get; set; }
        public HotkeyConfig? Hotkey { get; set; }
        public DebugConfig? Debug { get; set; }
    }

    /// <summary>
    /// Azure Speech configuration
    /// </summary>
    public class AzureSpeechConfig
    {
        public string? SubscriptionKey { get; set; }
        public string? Region { get; set; }
        public string? TranscriptionMode { get; set; }
        public FastTranscriptionConfig? FastTranscription { get; set; }
    }

    /// <summary>
    /// Fast transcription configuration
    /// </summary>
    public class FastTranscriptionConfig
    {
        public bool UseAzure { get; set; }
        public bool UseWhisper { get; set; }
        public string? ApiVersion { get; set; }
        public string? Endpoint { get; set; }
        public int TimeoutSeconds { get; set; }
        public int MaxRetries { get; set; }
        public bool OptimizeJsonParsing { get; set; }
        public bool SkipDetailedWordInfo { get; set; }
        public int ChunkOnSilenceDurationMs { get; set; }
        public int MinChunkDurationMs { get; set; }
        public int MaxChunkDurationMs { get; set; }
    }

    /// <summary>
    /// Debug configuration
    /// </summary>
    public class DebugConfig
    {
        public bool EnableKeyboardHookLogging { get; set; }
        public bool EnablePerformanceLogging { get; set; }
        public bool EnableAnimationSuspension { get; set; }
    }
}
