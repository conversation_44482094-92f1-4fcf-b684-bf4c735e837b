using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace PowerVoice.Configuration
{
    /// <summary>
    /// Represents modifier keys that can be combined with other keys
    /// </summary>
    [Flags]
    public enum ModifierKeys
    {
        None = 0,
        Ctrl = 1,
        Alt = 2,
        Shift = 4,
        Win = 8
    }

    /// <summary>
    /// Represents a key combination for hotkey configuration
    /// </summary>
    public class KeyCombination
    {
        /// <summary>
        /// The main key (non-modifier) in the combination
        /// </summary>
        public int VirtualKeyCode { get; set; }

        /// <summary>
        /// The modifier keys pressed with the main key
        /// </summary>
        public ModifierKeys Modifiers { get; set; }

        /// <summary>
        /// Human-readable name of the main key
        /// </summary>
        public string KeyName { get; set; } = string.Empty;

        /// <summary>
        /// Creates a new key combination
        /// </summary>
        public KeyCombination() { }

        /// <summary>
        /// Creates a new key combination with specified parameters
        /// </summary>
        public KeyCombination(int virtualKeyCode, ModifierKeys modifiers, string keyName)
        {
            VirtualKeyCode = virtualKeyCode;
            Modifiers = modifiers;
            KeyName = keyName;
        }

        /// <summary>
        /// Gets a human-readable string representation of the key combination
        /// </summary>
        public string GetDisplayString()
        {
            var parts = new List<string>();

            if (Modifiers.HasFlag(ModifierKeys.Ctrl))
                parts.Add("Ctrl");
            if (Modifiers.HasFlag(ModifierKeys.Alt))
                parts.Add("Alt");
            if (Modifiers.HasFlag(ModifierKeys.Shift))
                parts.Add("Shift");
            if (Modifiers.HasFlag(ModifierKeys.Win))
                parts.Add("Win");

            if (!string.IsNullOrEmpty(KeyName))
                parts.Add(KeyName);

            return string.Join(" + ", parts);
        }

        /// <summary>
        /// Checks if this key combination is valid
        /// </summary>
        public bool IsValid()
        {
            // Must have a main key
            if (VirtualKeyCode == 0 || string.IsNullOrEmpty(KeyName))
                return false;

            // Don't allow modifier-only combinations
            if (IsModifierKey(VirtualKeyCode))
                return false;

            return true;
        }

        /// <summary>
        /// Checks if the given virtual key code is a modifier key
        /// </summary>
        public static bool IsModifierKey(int vkCode)
        {
            return vkCode switch
            {
                0x10 => true, // VK_SHIFT
                0x11 => true, // VK_CONTROL
                0x12 => true, // VK_MENU (Alt)
                0x5B => true, // VK_LWIN
                0x5C => true, // VK_RWIN
                0xA0 => true, // VK_LSHIFT
                0xA1 => true, // VK_RSHIFT
                0xA2 => true, // VK_LCONTROL
                0xA3 => true, // VK_RCONTROL
                0xA4 => true, // VK_LMENU (Left Alt)
                0xA5 => true, // VK_RMENU (Right Alt)
                _ => false
            };
        }

        /// <summary>
        /// Checks if this key combination equals another
        /// </summary>
        public override bool Equals(object? obj)
        {
            if (obj is not KeyCombination other)
                return false;

            return VirtualKeyCode == other.VirtualKeyCode && Modifiers == other.Modifiers;
        }

        /// <summary>
        /// Gets hash code for this key combination
        /// </summary>
        public override int GetHashCode()
        {
            return HashCode.Combine(VirtualKeyCode, Modifiers);
        }

        /// <summary>
        /// String representation for debugging
        /// </summary>
        public override string ToString()
        {
            return $"KeyCombination: {GetDisplayString()} (VK: 0x{VirtualKeyCode:X2})";
        }
    }

    /// <summary>
    /// Configuration for hotkey settings
    /// </summary>
    public class HotkeyConfig
    {
        /// <summary>
        /// Whether to use the default Right Alt key
        /// </summary>
        public bool UseDefaultRightAlt { get; set; } = true;

        /// <summary>
        /// Custom key combination (used when UseDefaultRightAlt is false)
        /// </summary>
        public KeyCombination? CustomKeyCombination { get; set; }

        /// <summary>
        /// Gets the effective key combination to use
        /// </summary>
        [JsonIgnore]
        public KeyCombination EffectiveKeyCombination
        {
            get
            {
                if (UseDefaultRightAlt)
                {
                    return new KeyCombination(0xA5, ModifierKeys.None, "Right Alt");
                }
                return CustomKeyCombination ?? new KeyCombination(0xA5, ModifierKeys.None, "Right Alt");
            }
        }

        /// <summary>
        /// Validates the hotkey configuration
        /// </summary>
        public bool IsValid()
        {
            if (UseDefaultRightAlt)
                return true;

            return CustomKeyCombination?.IsValid() ?? false;
        }

        /// <summary>
        /// Gets a display string for the current hotkey configuration
        /// </summary>
        public string GetDisplayString()
        {
            return EffectiveKeyCombination.GetDisplayString();
        }

        /// <summary>
        /// Creates a default hotkey configuration
        /// </summary>
        public static HotkeyConfig CreateDefault()
        {
            return new HotkeyConfig
            {
                UseDefaultRightAlt = true,
                CustomKeyCombination = null
            };
        }

        /// <summary>
        /// Creates a custom hotkey configuration
        /// </summary>
        public static HotkeyConfig CreateCustom(KeyCombination keyCombination)
        {
            return new HotkeyConfig
            {
                UseDefaultRightAlt = false,
                CustomKeyCombination = keyCombination
            };
        }
    }

    /// <summary>
    /// Utility class for working with virtual key codes and key names
    /// </summary>
    public static class KeyCodeHelper
    {
        /// <summary>
        /// Gets a human-readable name for a virtual key code
        /// </summary>
        public static string GetKeyName(int vkCode)
        {
            return vkCode switch
            {
                // Special keys
                0x08 => "Backspace",
                0x09 => "Tab",
                0x0D => "Enter",
                0x1B => "Escape",
                0x20 => "Space",
                0x21 => "Page Up",
                0x22 => "Page Down",
                0x23 => "End",
                0x24 => "Home",
                0x25 => "Left Arrow",
                0x26 => "Up Arrow",
                0x27 => "Right Arrow",
                0x28 => "Down Arrow",
                0x2E => "Delete",
                0x2D => "Insert",

                // Modifier keys
                0x10 => "Shift",
                0x11 => "Ctrl",
                0x12 => "Alt",
                0x5B => "Left Win",
                0x5C => "Right Win",
                0xA0 => "Left Shift",
                0xA1 => "Right Shift",
                0xA2 => "Left Ctrl",
                0xA3 => "Right Ctrl",
                0xA4 => "Left Alt",
                0xA5 => "Right Alt",

                // Numbers 0-9
                >= 0x30 and <= 0x39 => ((char)vkCode).ToString(),

                // Letters A-Z
                >= 0x41 and <= 0x5A => ((char)vkCode).ToString(),

                // Numpad 0-9
                >= 0x60 and <= 0x69 => $"Numpad {(char)(vkCode - 0x60 + '0')}",

                // Function keys F1-F24
                >= 0x70 and <= 0x87 => $"F{vkCode - 0x6F}",

                // Punctuation and symbols
                0xBA => ";",
                0xBB => "=",
                0xBC => ",",
                0xBD => "-",
                0xBE => ".",
                0xBF => "/",
                0xC0 => "`",
                0xDB => "[",
                0xDC => "\\",
                0xDD => "]",
                0xDE => "'",

                // Default case
                _ => $"Key_{vkCode:X2}"
            };
        }

        /// <summary>
        /// Gets modifier keys from the current keyboard state
        /// </summary>
        public static ModifierKeys GetCurrentModifiers()
        {
            var modifiers = ModifierKeys.None;

            if ((GetAsyncKeyState(0x11) & 0x8000) != 0) // VK_CONTROL
                modifiers |= ModifierKeys.Ctrl;
            if ((GetAsyncKeyState(0x12) & 0x8000) != 0) // VK_MENU (Alt)
                modifiers |= ModifierKeys.Alt;
            if ((GetAsyncKeyState(0x10) & 0x8000) != 0) // VK_SHIFT
                modifiers |= ModifierKeys.Shift;
            if ((GetAsyncKeyState(0x5B) & 0x8000) != 0 || (GetAsyncKeyState(0x5C) & 0x8000) != 0) // VK_LWIN or VK_RWIN
                modifiers |= ModifierKeys.Win;

            return modifiers;
        }

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        /// <summary>
        /// Parses a key combination from a display string (e.g., "Ctrl + U")
        /// </summary>
        public static KeyCombination? ParseFromDisplayString(string displayString)
        {
            if (string.IsNullOrWhiteSpace(displayString))
                return null;

            var parts = displayString.Split(new[] { " + ", "+" }, StringSplitOptions.RemoveEmptyEntries)
                                   .Select(p => p.Trim())
                                   .ToArray();

            if (parts.Length == 0)
                return null;

            var modifiers = ModifierKeys.None;
            string? mainKey = null;

            foreach (var part in parts)
            {
                switch (part.ToLowerInvariant())
                {
                    case "ctrl":
                    case "control":
                        modifiers |= ModifierKeys.Ctrl;
                        break;
                    case "alt":
                        modifiers |= ModifierKeys.Alt;
                        break;
                    case "shift":
                        modifiers |= ModifierKeys.Shift;
                        break;
                    case "win":
                    case "windows":
                        modifiers |= ModifierKeys.Win;
                        break;
                    default:
                        if (mainKey == null)
                            mainKey = part;
                        break;
                }
            }

            if (mainKey == null)
                return null;

            // Try to find the virtual key code for the main key
            int vkCode = GetVirtualKeyCodeFromName(mainKey);
            if (vkCode == 0)
                return null;

            return new KeyCombination(vkCode, modifiers, mainKey);
        }

        /// <summary>
        /// Gets the virtual key code from a key name
        /// </summary>
        private static int GetVirtualKeyCodeFromName(string keyName)
        {
            if (string.IsNullOrEmpty(keyName))
                return 0;

            // Handle special cases
            switch (keyName.ToLowerInvariant())
            {
                case "space": return 0x20;
                case "enter": return 0x0D;
                case "escape": case "esc": return 0x1B;
                case "tab": return 0x09;
                case "backspace": return 0x08;
                case "delete": case "del": return 0x2E;
                case "insert": case "ins": return 0x2D;
                case "home": return 0x24;
                case "end": return 0x23;
                case "page up": case "pageup": case "pgup": return 0x21;
                case "page down": case "pagedown": case "pgdn": return 0x22;
                case "left arrow": case "left": return 0x25;
                case "up arrow": case "up": return 0x26;
                case "right arrow": case "right": return 0x27;
                case "down arrow": case "down": return 0x28;
            }

            // Handle function keys
            if (keyName.ToLowerInvariant().StartsWith('f') &&
                int.TryParse(keyName.Substring(1), out int fKeyNum) &&
                fKeyNum >= 1 && fKeyNum <= 24)
            {
                return 0x6F + fKeyNum; // F1 = 0x70, F2 = 0x71, etc.
            }

            // Handle single characters
            if (keyName.Length == 1)
            {
                char c = char.ToUpperInvariant(keyName[0]);
                if (c >= 'A' && c <= 'Z')
                    return c; // A-Z virtual key codes
                if (c >= '0' && c <= '9')
                    return c; // 0-9 virtual key codes
            }

            return 0; // Unknown key
        }

        /// <summary>
        /// Validates if a key combination is suitable for use as a hotkey
        /// </summary>
        public static bool IsValidHotkey(KeyCombination keyCombination)
        {
            if (!keyCombination.IsValid())
                return false;

            // Don't allow certain problematic keys
            var problematicKeys = new[]
            {
                0x10, // VK_SHIFT
                0x11, // VK_CONTROL
                0x12, // VK_MENU (Alt)
                0x5B, // VK_LWIN
                0x5C, // VK_RWIN
                0x1B, // VK_ESCAPE (might interfere with canceling operations)
            };

            if (problematicKeys.Contains(keyCombination.VirtualKeyCode))
                return false;

            // Require at least one modifier for most keys to avoid conflicts
            if (keyCombination.Modifiers == ModifierKeys.None)
            {
                // Allow function keys without modifiers
                if (keyCombination.VirtualKeyCode >= 0x70 && keyCombination.VirtualKeyCode <= 0x87)
                    return true;

                // Allow Right Alt without modifiers (for backward compatibility)
                if (keyCombination.VirtualKeyCode == 0xA5)
                    return true;

                // Don't allow other keys without modifiers
                return false;
            }

            return true;
        }
    }
}
