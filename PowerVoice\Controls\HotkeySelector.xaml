<UserControl x:Class="PowerVoice.Controls.HotkeySelector"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="120" d:DesignWidth="400">

    <UserControl.Resources>
        <!-- Styles for the hotkey selector -->
        <Style x:Key="HotkeyButton" TargetType="Button">
            <Setter Property="Background" Value="#F8FAFC"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F1F5F9"/>
                                <Setter Property="BorderBrush" Value="#CBD5E1"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E2E8F0"/>
                                <Setter Property="BorderBrush" Value="#94A3B8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="RecordingButton" TargetType="Button" BasedOn="{StaticResource HotkeyButton}">
            <Setter Property="Background" Value="#FEF3C7"/>
            <Setter Property="BorderBrush" Value="#F59E0B"/>
            <Setter Property="Foreground" Value="#92400E"/>
        </Style>

        <Style x:Key="ResetButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#6B7280"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F3F4F6"/>
                                <Setter Property="Foreground" Value="#374151"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                      Text="Activation Hotkey"
                      FontSize="14"
                      FontWeight="Medium"
                      Foreground="#374151"
                      VerticalAlignment="Center"/>

            <Button x:Name="ResetButton"
                   Grid.Column="1"
                   Content="Reset to Default"
                   Style="{StaticResource ResetButton}"
                   Click="ResetButton_Click"
                   ToolTip="Reset to Right Alt key"/>
        </Grid>

        <!-- Hotkey Display/Input Button -->
        <Button x:Name="HotkeyButton"
               Grid.Row="1"
               Height="48"
               Click="HotkeyButton_Click"
               Style="{StaticResource HotkeyButton}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="HotkeyDisplayText"
                          Grid.Column="0"
                          Text="Right Alt"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Left"/>

                <TextBlock Grid.Column="1"
                          Text="Click to change"
                          FontSize="12"
                          Foreground="#6B7280"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Right"/>
            </Grid>
        </Button>

        <!-- Status/Help Text -->
        <TextBlock x:Name="StatusText"
                  Grid.Row="2"
                  Text="Press any key combination while recording to set a custom hotkey"
                  FontSize="12"
                  Foreground="#6B7280"
                  Margin="0,8,0,0"
                  TextWrapping="Wrap"/>
    </Grid>
</UserControl>
