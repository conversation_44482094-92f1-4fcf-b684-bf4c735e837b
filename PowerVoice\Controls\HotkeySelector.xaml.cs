using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using PowerVoice.Configuration;

namespace PowerVoice.Controls
{
    /// <summary>
    /// Interaction logic for HotkeySelector.xaml
    /// </summary>
    public partial class HotkeySelector : UserControl
    {
        private bool _isRecording = false;
        private HotkeyConfig _currentConfig = HotkeyConfig.CreateDefault();

        /// <summary>
        /// Event fired when the hotkey configuration changes
        /// </summary>
        public event EventHandler<HotkeyConfig>? HotkeyChanged;

        /// <summary>
        /// Gets or sets the current hotkey configuration
        /// </summary>
        public HotkeyConfig CurrentHotkeyConfig
        {
            get => _currentConfig;
            set
            {
                _currentConfig = value ?? HotkeyConfig.CreateDefault();
                UpdateDisplay();
            }
        }

        public HotkeySelector()
        {
            InitializeComponent();
            UpdateDisplay();
        }

        /// <summary>
        /// Updates the display to show the current hotkey configuration
        /// </summary>
        private void UpdateDisplay()
        {
            if (HotkeyDisplayText != null)
            {
                HotkeyDisplayText.Text = _currentConfig.GetDisplayString();
            }

            if (StatusText != null)
            {
                if (_isRecording)
                {
                    StatusText.Text = "Recording... Press any key combination (Esc to cancel)";
                    StatusText.Foreground = new SolidColorBrush(Color.FromRgb(0x92, 0x40, 0x0E)); // Amber-800
                }
                else
                {
                    StatusText.Text = "Press any key combination while recording to set a custom hotkey";
                    StatusText.Foreground = new SolidColorBrush(Color.FromRgb(0x6B, 0x72, 0x80)); // Gray-500
                }
            }

            // Update button style based on recording state
            if (HotkeyButton != null)
            {
                if (_isRecording)
                {
                    HotkeyButton.Style = (Style)FindResource("RecordingButton");
                }
                else
                {
                    HotkeyButton.Style = (Style)FindResource("HotkeyButton");
                }
            }
        }

        /// <summary>
        /// Handles the hotkey button click to start/stop recording
        /// </summary>
        private void HotkeyButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isRecording)
            {
                StopRecording();
            }
            else
            {
                StartRecording();
            }
        }

        /// <summary>
        /// Starts recording a new hotkey
        /// </summary>
        private void StartRecording()
        {
            _isRecording = true;
            UpdateDisplay();

            // Focus the button to capture key events
            HotkeyButton.Focus();

            // Capture keyboard events
            HotkeyButton.KeyDown += HotkeyButton_KeyDown;
            HotkeyButton.LostFocus += HotkeyButton_LostFocus;
        }

        /// <summary>
        /// Stops recording and restores normal state
        /// </summary>
        private void StopRecording()
        {
            _isRecording = false;
            UpdateDisplay();

            // Remove event handlers
            HotkeyButton.KeyDown -= HotkeyButton_KeyDown;
            HotkeyButton.LostFocus -= HotkeyButton_LostFocus;
        }

        /// <summary>
        /// Handles key down events during recording
        /// </summary>
        private void HotkeyButton_KeyDown(object sender, KeyEventArgs e)
        {
            if (!_isRecording) return;

            // Handle Escape to cancel recording
            if (e.Key == Key.Escape)
            {
                StopRecording();
                e.Handled = true;
                return;
            }

            // Convert WPF Key to virtual key code
            int vkCode = KeyInterop.VirtualKeyFromKey(e.Key);

            // Skip modifier-only keys
            if (KeyCombination.IsModifierKey(vkCode))
            {
                e.Handled = true;
                return;
            }

            // Get current modifiers
            var modifiers = GetCurrentModifiers();

            // Get key name
            string keyName = KeyCodeHelper.GetKeyName(vkCode);

            // Create new key combination
            var newKeyCombination = new KeyCombination(vkCode, modifiers, keyName);

            // Validate the combination
            if (newKeyCombination.IsValid())
            {
                // Update configuration
                _currentConfig = HotkeyConfig.CreateCustom(newKeyCombination);

                // Fire change event
                HotkeyChanged?.Invoke(this, _currentConfig);

                // Stop recording
                StopRecording();
            }

            e.Handled = true;
        }

        /// <summary>
        /// Handles focus lost during recording (cancels recording)
        /// </summary>
        private void HotkeyButton_LostFocus(object sender, RoutedEventArgs e)
        {
            if (_isRecording)
            {
                StopRecording();
            }
        }

        /// <summary>
        /// Gets the current modifier keys state
        /// </summary>
        private Configuration.ModifierKeys GetCurrentModifiers()
        {
            var modifiers = Configuration.ModifierKeys.None;

            if (Keyboard.IsKeyDown(Key.LeftCtrl) || Keyboard.IsKeyDown(Key.RightCtrl))
                modifiers |= Configuration.ModifierKeys.Ctrl;
            if (Keyboard.IsKeyDown(Key.LeftAlt) || Keyboard.IsKeyDown(Key.RightAlt))
                modifiers |= Configuration.ModifierKeys.Alt;
            if (Keyboard.IsKeyDown(Key.LeftShift) || Keyboard.IsKeyDown(Key.RightShift))
                modifiers |= Configuration.ModifierKeys.Shift;
            if (Keyboard.IsKeyDown(Key.LWin) || Keyboard.IsKeyDown(Key.RWin))
                modifiers |= Configuration.ModifierKeys.Win;

            return modifiers;
        }

        /// <summary>
        /// Handles the reset button click
        /// </summary>
        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            // Reset to default Right Alt configuration
            _currentConfig = HotkeyConfig.CreateDefault();
            UpdateDisplay();

            // Fire change event
            HotkeyChanged?.Invoke(this, _currentConfig);
        }

        /// <summary>
        /// Override to handle key events when the control has focus
        /// </summary>
        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (_isRecording)
            {
                HotkeyButton_KeyDown(this, e);
            }
            else
            {
                base.OnKeyDown(e);
            }
        }
    }
}
