using System.Diagnostics;

namespace PowerVoice.Extensions
{
    /// <summary>
    /// Helper methods for Debug class to provide timestamped logging
    /// </summary>
    public static class DebugT
    {
        /// <summary>
        /// Writes a timestamped line to the debug output
        /// Automatically adds timestamp in HH:mm:ss:fff format with tab separator
        /// </summary>
        /// <param name="message">Message to write</param>
        public static void WriteLine(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss:fff");
            Debug.WriteLine($"{timestamp}\t{message}");
        }

        /// <summary>
        /// Writes a timestamped line to the debug output with formatted string
        /// Format: HH:mm:ss:fff[TAB]formatted_message
        /// </summary>
        /// <param name="format">Format string</param>
        /// <param name="args">Arguments for formatting</param>
        public static void WriteLine(string format, params object[] args)
        {
            var message = string.Format(format, args);
            var timestamp = DateTime.Now.ToString("HH:mm:ss:fff");
            Debug.WriteLine($"{timestamp}\t{message}");
        }
    }
}
