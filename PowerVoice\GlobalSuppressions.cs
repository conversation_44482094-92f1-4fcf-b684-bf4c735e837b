// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

// Suppress specific warnings only when absolutely necessary
// Example: [assembly: SuppressMessage("Category", "RuleId", Justification = "Reason", Scope = "member", Target = "~M:...")]

// Note: Avoid suppressions when possible. These should only be used for:
// 1. Generated code that cannot be modified
// 2. Platform-specific code requirements
// 3. External library compatibility issues
// 4. Performance-critical code where the warning is intentionally ignored

// Example suppressions (commented out - only enable if needed):
// [assembly: SuppressMessage("Performance", "CA1822:Mark members as static", 
//     Justification = "Event handlers must be instance methods", 
//     Scope = "member", Target = "~M:PowerVoice.MainWindow.RecordButton_Click(System.Object,System.Windows.RoutedEventArgs)")]
