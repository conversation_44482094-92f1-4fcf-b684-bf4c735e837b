using System;
using System.Runtime.InteropServices;
using PowerVoice.Configuration;
using PowerVoice.Extensions;

namespace PowerVoice.Hotkey
{
    /// <summary>
    /// Manages hotkey detection with dual code paths for Right Alt and custom key combinations
    /// </summary>
    public class HotkeyManager : IDisposable
    {
        // Windows API constants
        private const int WH_KEYBOARD_LL = 13;
        private const int WM_KEYDOWN = 0x0100;
        private const int WM_KEYUP = 0x0101;
        private const int WM_SYSKEYDOWN = 0x0104;
        private const int WM_SYSKEYUP = 0x0105;

        // Hook procedure delegate
        public delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);

        // Windows API declarations
        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        // Hook management
        private IntPtr _hookID = IntPtr.Zero;
        private LowLevelKeyboardProc _proc;
        private HotkeyConfig _currentHotkeyConfig = HotkeyConfig.CreateDefault();
        private bool _isHotkeyPressed = false;

        // Events
        public event EventHandler? HotkeyPressed;
        public event EventHandler? HotkeyReleased;

        /// <summary>
        /// Gets the current hotkey configuration
        /// </summary>
        public HotkeyConfig CurrentHotkeyConfig => _currentHotkeyConfig;

        public HotkeyManager()
        {
            _proc = HookCallback;
        }

        /// <summary>
        /// Updates the hotkey configuration
        /// </summary>
        public void UpdateHotkeyConfig(HotkeyConfig config)
        {
            _currentHotkeyConfig = config ?? HotkeyConfig.CreateDefault();
            DebugT.WriteLine($"🔧 Hotkey configuration updated: {_currentHotkeyConfig.GetDisplayString()}");
        }

        /// <summary>
        /// Starts the keyboard hook
        /// </summary>
        public void StartHook()
        {
            try
            {
                if (_hookID != IntPtr.Zero)
                {
                    DebugT.WriteLine("⚠️ Hook already started");
                    return;
                }

                _hookID = SetWindowsHookEx(WH_KEYBOARD_LL, _proc,
                    GetModuleHandle(null), 0);

                if (_hookID == IntPtr.Zero)
                {
                    throw new InvalidOperationException("Failed to install keyboard hook");
                }

                DebugT.WriteLine($"✅ Hotkey detection started for: {_currentHotkeyConfig.GetDisplayString()}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Failed to start hotkey detection: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Stops the keyboard hook
        /// </summary>
        public void StopHook()
        {
            try
            {
                if (_hookID != IntPtr.Zero)
                {
                    UnhookWindowsHookEx(_hookID);
                    _hookID = IntPtr.Zero;
                    DebugT.WriteLine("✅ Hotkey detection stopped");
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error stopping hotkey detection: {ex.Message}");
            }
        }

        /// <summary>
        /// Low-level keyboard hook callback
        /// </summary>
        private IntPtr HookCallback(int nCode, IntPtr wParam, IntPtr lParam)
        {
            if (nCode >= 0)
            {
                int vkCode = Marshal.ReadInt32(lParam);

                // Use different detection logic based on hotkey configuration
                if (_currentHotkeyConfig.UseDefaultRightAlt)
                {
                    return HandleRightAltDetection(nCode, wParam, lParam, vkCode);
                }
                else
                {
                    return HandleCustomHotkeyDetection(nCode, wParam, lParam, vkCode);
                }
            }

            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }

        /// <summary>
        /// Handles Right Alt detection with existing workarounds
        /// </summary>
        private IntPtr HandleRightAltDetection(int nCode, IntPtr wParam, IntPtr lParam, int vkCode)
        {
            // CRITICAL: Intercept and consume ONLY RIGHT Alt key to prevent menu activation
            if (vkCode == 0xA5) // VK_RMENU (Right Alt only)
            {
                bool altKeyDown = false;
                string altEventType = "";

                // Handle all possible keyboard message types for Right Alt key
                if (wParam == (IntPtr)WM_KEYDOWN)
                {
                    altKeyDown = true;
                    altEventType = "KeyDown";
                }
                else if (wParam == (IntPtr)WM_KEYUP)
                {
                    altKeyDown = false;
                    altEventType = "KeyUp";
                }
                else if (wParam == (IntPtr)WM_SYSKEYDOWN)
                {
                    altKeyDown = true;
                    altEventType = "SysKeyDown";
                }
                else if (wParam == (IntPtr)WM_SYSKEYUP)
                {
                    altKeyDown = false;
                    altEventType = "SysKeyUp";
                }

                // Fire events based on key state
                if (altEventType == "SysKeyDown" && !_isHotkeyPressed)
                {
                    _isHotkeyPressed = true;
                    HotkeyPressed?.Invoke(this, EventArgs.Empty);
                    DebugT.WriteLine("🔑 Right Alt hotkey PRESSED");
                }
                else if ((altEventType == "SysKeyUp" || altEventType == "KeyUp") && _isHotkeyPressed)
                {
                    _isHotkeyPressed = false;
                    HotkeyReleased?.Invoke(this, EventArgs.Empty);
                    DebugT.WriteLine("🔑 Right Alt hotkey RELEASED");
                }

                // CONSUME ONLY Right Alt events - do NOT pass to other applications
                return (IntPtr)1;
            }

            // Pass all non-Right Alt events to other applications normally
            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }

        /// <summary>
        /// Handles custom hotkey detection without Alt workarounds
        /// </summary>
        private IntPtr HandleCustomHotkeyDetection(int nCode, IntPtr wParam, IntPtr lParam, int vkCode)
        {
            var customKey = _currentHotkeyConfig.CustomKeyCombination;
            if (customKey == null || !customKey.IsValid())
            {
                return CallNextHookEx(_hookID, nCode, wParam, lParam);
            }

            // Check if this is our target key
            if (vkCode == customKey.VirtualKeyCode)
            {
                bool isKeyDown = wParam == (IntPtr)WM_KEYDOWN || wParam == (IntPtr)WM_SYSKEYDOWN;
                bool isKeyUp = wParam == (IntPtr)WM_KEYUP || wParam == (IntPtr)WM_SYSKEYUP;

                if (isKeyDown || isKeyUp)
                {
                    // Check if required modifiers are pressed
                    bool modifiersMatch = CheckModifiersMatch(customKey.Modifiers);

                    if (modifiersMatch)
                    {
                        if (isKeyDown && !_isHotkeyPressed)
                        {
                            _isHotkeyPressed = true;
                            HotkeyPressed?.Invoke(this, EventArgs.Empty);
                            DebugT.WriteLine($"🔑 Custom hotkey PRESSED: {customKey.GetDisplayString()}");

                            // Consume the key event to prevent it from reaching other applications
                            return (IntPtr)1;
                        }
                        else if (isKeyUp && _isHotkeyPressed)
                        {
                            _isHotkeyPressed = false;
                            HotkeyReleased?.Invoke(this, EventArgs.Empty);
                            DebugT.WriteLine($"🔑 Custom hotkey RELEASED: {customKey.GetDisplayString()}");

                            // Consume the key event to prevent it from reaching other applications
                            return (IntPtr)1;
                        }
                    }
                }
            }

            // Pass all other events to other applications normally
            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }

        /// <summary>
        /// Checks if the current modifier key state matches the required modifiers
        /// </summary>
        private bool CheckModifiersMatch(ModifierKeys requiredModifiers)
        {
            bool ctrlPressed = (GetAsyncKeyState(0x11) & 0x8000) != 0; // VK_CONTROL
            bool altPressed = (GetAsyncKeyState(0x12) & 0x8000) != 0;  // VK_MENU
            bool shiftPressed = (GetAsyncKeyState(0x10) & 0x8000) != 0; // VK_SHIFT
            bool winPressed = (GetAsyncKeyState(0x5B) & 0x8000) != 0 || (GetAsyncKeyState(0x5C) & 0x8000) != 0; // VK_LWIN or VK_RWIN

            bool ctrlRequired = requiredModifiers.HasFlag(ModifierKeys.Ctrl);
            bool altRequired = requiredModifiers.HasFlag(ModifierKeys.Alt);
            bool shiftRequired = requiredModifiers.HasFlag(ModifierKeys.Shift);
            bool winRequired = requiredModifiers.HasFlag(ModifierKeys.Win);

            return (ctrlPressed == ctrlRequired) &&
                   (altPressed == altRequired) &&
                   (shiftPressed == shiftRequired) &&
                   (winPressed == winRequired);
        }

        /// <summary>
        /// Disposes the hotkey manager
        /// </summary>
        public void Dispose()
        {
            StopHook();
            GC.SuppressFinalize(this);
        }
    }
}
