<Window x:Class="PowerVoice.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PowerVoice"
        mc:Ignorable="d"
        Title="PowerVoice - Professional Dictation"
        Height="200" Width="360"
        MinHeight="200" MinWidth="360"
        MaxHeight="200" MaxWidth="360"
        WindowStyle="None"
        AllowsTransparency="False"
        Background="White"
        Topmost="True"
        ShowInTaskbar="False"
        ResizeMode="NoResize"
        MouseLeftButtonDown="Window_MouseLeftButtonDown"
        Icon="powervoice-modern.ico">

    <Window.Resources>
        <!-- Modern Color Palette -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#6366F1"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#A855F7"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#EC4899"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#F8FAFC"/>
        <SolidColorBrush x:Key="OnSurfaceBrush" Color="#0F172A"/>
        <SolidColorBrush x:Key="SubtleBrush" Color="#64748B"/>

        <!-- Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#8B5CF6" Offset="0"/>
            <GradientStop Color="#6366F1" Offset="0.5"/>
            <GradientStop Color="#3B82F6" Offset="1"/>
        </LinearGradientBrush>

        <RadialGradientBrush x:Key="GlowBrush" Center="0.5,0.5" RadiusX="1" RadiusY="1">
            <GradientStop Color="#4F46E555" Offset="0"/>
            <GradientStop Color="#4F46E522" Offset="0.7"/>
            <GradientStop Color="#4F46E500" Offset="1"/>
        </RadialGradientBrush>

        <!-- Microphone Pulse Animation -->
        <Storyboard x:Key="MicrophonePulse" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="MicrophoneCircle"
                           Storyboard.TargetProperty="Opacity"
                           From="1.0" To="0.8" Duration="0:0:1.2"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="MicrophoneCircle"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1.0" To="1.06" Duration="0:0:1.2"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="MicrophoneCircle"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1.0" To="1.06" Duration="0:0:1.2"
                           AutoReverse="True"/>
        </Storyboard>

        <!-- Listening Wave Animation -->
        <Storyboard x:Key="ListeningWave" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="WaveRing1"
                           Storyboard.TargetProperty="Opacity"
                           From="0.2" To="0.7" Duration="0:0:1"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="WaveRing1"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1.0" To="1.2" Duration="0:0:1"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="WaveRing1"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1.0" To="1.2" Duration="0:0:1"
                           AutoReverse="True"/>
            <!-- WaveRing2 with different timing for layered effect -->
            <DoubleAnimation Storyboard.TargetName="WaveRing2"
                           Storyboard.TargetProperty="Opacity"
                           From="0.1" To="0.5" Duration="0:0:1.3"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="WaveRing2"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1.0" To="1.4" Duration="0:0:1.3"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="WaveRing2"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1.0" To="1.4" Duration="0:0:1.3"
                           AutoReverse="True"/>
        </Storyboard>

        <!-- Beautiful Speaking Animation - Soft Moving Waves -->
        <Storyboard x:Key="SpeakingAnimation" RepeatBehavior="Forever">
            <!-- Soft breathing animation on microphone itself -->
            <DoubleAnimation Storyboard.TargetName="MicrophoneIcon"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1.0" To="1.08" Duration="0:0:1.5"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="MicrophoneIcon"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1.0" To="1.08" Duration="0:0:1.5"
                           AutoReverse="True"/>

            <!-- Soft glow breathing effect -->
            <DoubleAnimation Storyboard.TargetName="SpeakingGlow"
                           Storyboard.TargetProperty="Opacity"
                           From="0.2" To="0.6" Duration="0:0:1.5"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="SpeakingGlow"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1.0" To="1.15" Duration="0:0:1.5"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="SpeakingGlow"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1.0" To="1.15" Duration="0:0:1.5"
                           AutoReverse="True"/>

            <!-- Animated speaking wave 1 -->
            <DoubleAnimation Storyboard.TargetName="SpeakingWave1"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="0.8" To="1.3" Duration="0:0:2"
                           AutoReverse="False"/>
            <DoubleAnimation Storyboard.TargetName="SpeakingWave1"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="0.8" To="1.3" Duration="0:0:2"
                           AutoReverse="False"/>
            <DoubleAnimation Storyboard.TargetName="SpeakingWave1"
                           Storyboard.TargetProperty="Opacity"
                           From="0.6" To="0" Duration="0:0:2"
                           AutoReverse="False"/>

            <!-- Animated speaking wave 2 (delayed) -->
            <DoubleAnimation Storyboard.TargetName="SpeakingWave2"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="0.8" To="1.3" Duration="0:0:2"
                           BeginTime="0:0:0.7"
                           AutoReverse="False"/>
            <DoubleAnimation Storyboard.TargetName="SpeakingWave2"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="0.8" To="1.3" Duration="0:0:2"
                           BeginTime="0:0:0.7"
                           AutoReverse="False"/>
            <DoubleAnimation Storyboard.TargetName="SpeakingWave2"
                           Storyboard.TargetProperty="Opacity"
                           From="0.4" To="0" Duration="0:0:2"
                           BeginTime="0:0:0.7"
                           AutoReverse="False"/>

            <!-- Animated speaking wave 3 (more delayed) -->
            <DoubleAnimation Storyboard.TargetName="SpeakingWave3"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="0.8" To="1.3" Duration="0:0:2"
                           BeginTime="0:0:1.4"
                           AutoReverse="False"/>
            <DoubleAnimation Storyboard.TargetName="SpeakingWave3"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="0.8" To="1.3" Duration="0:0:2"
                           BeginTime="0:0:1.4"
                           AutoReverse="False"/>
            <DoubleAnimation Storyboard.TargetName="SpeakingWave3"
                           Storyboard.TargetProperty="Opacity"
                           From="0.3" To="0" Duration="0:0:2"
                           BeginTime="0:0:1.4"
                           AutoReverse="False"/>
        </Storyboard>

        <!-- Hover Animations -->
        <Storyboard x:Key="ButtonHoverIn">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           To="0.8" Duration="0:0:0.15"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1.1" Duration="0:0:0.15"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1.1" Duration="0:0:0.15"/>
        </Storyboard>

        <Storyboard x:Key="ButtonHoverOut">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           To="1.0" Duration="0:0:0.15"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1.0" Duration="0:0:0.15"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1.0" Duration="0:0:0.15"/>
        </Storyboard>

        <!-- Modern Button Style -->
        <Style x:Key="ModernIconButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <ScaleTransform/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <Ellipse Fill="#F1F5F9"
                                   Width="{TemplateBinding Width}"
                                   Height="{TemplateBinding Height}"
                                   Opacity="0"/>
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard Storyboard="{StaticResource ButtonHoverIn}"/>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard Storyboard="{StaticResource ButtonHoverOut}"/>
                                </Trigger.ExitActions>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Modern Glassmorphism Container -->
    <Grid>
        <!-- Subtle Shadow Layer -->
        <Border CornerRadius="24"
                Background="#10000000"
                Margin="2,4,2,8">
        </Border>

        <!-- Main Glass Container -->
        <Border CornerRadius="24"
                Background="#F5FFFFFF"
                BorderThickness="1"
                BorderBrush="#30FFFFFF"
                Margin="0,0,0,4">

            <!-- Inner Glass Layer -->
            <Border CornerRadius="23"
                    Background="{StaticResource GlowBrush}"
                    Margin="1">

                <!-- Content Container -->
                <Border CornerRadius="22"
                        Background="#FAFFFFFF"
                        Margin="1">

                    <!-- Main Content Grid -->
                    <Grid Margin="24,20,24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Modern Header -->
                        <Grid Grid.Row="0" Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Brand Section -->
                            <StackPanel Grid.Column="0"
                                      VerticalAlignment="Center"
                                      Orientation="Horizontal">
                                <!-- Modern App Icon -->
                                <Grid Width="28" Height="28" Margin="0,0,10,0">
                                    <!-- Icon Background with Gradient -->
                                    <Ellipse Fill="{StaticResource PrimaryGradient}"
                                           Width="28" Height="28"/>
                                    <!-- Icon Content -->
                                    <Path Fill="#FFFFFF"
                                          Width="14" Height="14"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Data="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"
                                          Stretch="Uniform"/>
                                </Grid>

                                <!-- App Title -->
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="PowerVoice"
                                             FontSize="15"
                                             FontWeight="Bold"
                                             Foreground="{StaticResource OnSurfaceBrush}"
                                             FontFamily="Segoe UI Variable Display"/>
                                </StackPanel>

                                <!-- Mode Badge -->
                                <Border x:Name="ModeBadge"
                                        Background="#E5E7EB"
                                        CornerRadius="6"
                                        Padding="6,2"
                                        Margin="10,0,0,0"
                                        VerticalAlignment="Center">
                                    <TextBlock x:Name="ModeBadgeText"
                                               Text="Fast"
                                               FontSize="12"
                                               FontWeight="SemiBold"
                                               Foreground="#111827"/>
                                </Border>
                            </StackPanel>

                            <!-- Action Buttons -->
                            <Button Grid.Column="1"
                                   Style="{StaticResource ModernIconButton}"
                                   Width="28" Height="28"
                                   Margin="0,0,8,0"
                                   ToolTip="Settings"
                                   Click="Settings_Click">
                                <Path Fill="{StaticResource SubtleBrush}"
                                      Width="14" Height="14"
                                      Data="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"
                                      Stretch="Uniform"/>
                            </Button>

                            <Button Grid.Column="2"
                                   Style="{StaticResource ModernIconButton}"
                                   Width="28" Height="28"
                                   ToolTip="Minimize to Tray"
                                   Click="MinimizeToTray_Click">
                                <Path Fill="{StaticResource SubtleBrush}"
                                      Width="14" Height="14"
                                      Data="M19,13H5V11H19V13Z"
                                      Stretch="Uniform"/>
                            </Button>
                        </Grid>

                        <!-- Main Status Area -->
                        <Grid Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">

                            <!-- Enhanced Microphone Visualization -->
                            <Grid VerticalAlignment="Center" HorizontalAlignment="Center">
                                <Canvas Width="80" Height="80">
                                    <!-- Outer Glow Ring -->
                                    <Ellipse x:Name="OuterGlow"
                                            Width="100" Height="100"
                                            Canvas.Left="-10" Canvas.Top="-10"
                                            Fill="{StaticResource GlowBrush}"
                                            Opacity="0"
                                            Visibility="Collapsed">
                                        <Ellipse.RenderTransform>
                                            <ScaleTransform CenterX="50" CenterY="50"/>
                                        </Ellipse.RenderTransform>
                                    </Ellipse>

                                    <!-- Primary Wave Ring -->
                                    <Ellipse x:Name="WaveRing1"
                                            Width="90" Height="90"
                                            Canvas.Left="-5" Canvas.Top="-5"
                                            Stroke="{StaticResource PrimaryBrush}"
                                            StrokeThickness="2"
                                            Fill="Transparent"
                                            Opacity="0"
                                            Visibility="Collapsed">
                                        <Ellipse.RenderTransform>
                                            <ScaleTransform CenterX="45" CenterY="45"/>
                                        </Ellipse.RenderTransform>
                                    </Ellipse>

                                    <!-- Secondary Wave Ring -->
                                    <Ellipse x:Name="WaveRing2"
                                            Width="110" Height="110"
                                            Canvas.Left="-15" Canvas.Top="-15"
                                            Stroke="{StaticResource SecondaryBrush}"
                                            StrokeThickness="1.5"
                                            Fill="Transparent"
                                            Opacity="0"
                                            Visibility="Collapsed">
                                        <Ellipse.RenderTransform>
                                            <ScaleTransform CenterX="55" CenterY="55"/>
                                        </Ellipse.RenderTransform>
                                    </Ellipse>

                                    <!-- Main Microphone Container -->
                                    <Grid Width="80" Height="80" Canvas.Left="0" Canvas.Top="0">
                                        <!-- Background Circle with Gradient -->
                                        <Ellipse x:Name="MicrophoneCircle"
                                                Width="80" Height="80"
                                                Fill="#FFFFFF"
                                                Stroke="#E2E8F0"
                                                StrokeThickness="3">
                                            <Ellipse.RenderTransform>
                                                <ScaleTransform CenterX="40" CenterY="40"/>
                                            </Ellipse.RenderTransform>
                                        </Ellipse>

                                        <!-- Beautiful Speaking Glow (hidden by default) -->
                                        <Ellipse x:Name="SpeakingGlow"
                                                Width="90" Height="90"
                                                Fill="#A855F7"
                                                Opacity="0"
                                                Visibility="Collapsed"
                                                Margin="-5">
                                            <Ellipse.RenderTransform>
                                                <ScaleTransform CenterX="45" CenterY="45"/>
                                            </Ellipse.RenderTransform>
                                        </Ellipse>

                                        <!-- Speaking Wave Elements (beautiful soft waves) -->
                                        <Ellipse x:Name="SpeakingWave1"
                                                Width="70" Height="70"
                                                Stroke="#A855F7"
                                                StrokeThickness="1.5"
                                                Fill="Transparent"
                                                Opacity="0"
                                                Visibility="Collapsed"
                                                Margin="5">
                                            <Ellipse.RenderTransform>
                                                <ScaleTransform CenterX="35" CenterY="35"/>
                                            </Ellipse.RenderTransform>
                                        </Ellipse>

                                        <Ellipse x:Name="SpeakingWave2"
                                                Width="70" Height="70"
                                                Stroke="#A855F7"
                                                StrokeThickness="1"
                                                Fill="Transparent"
                                                Opacity="0"
                                                Visibility="Collapsed"
                                                Margin="5">
                                            <Ellipse.RenderTransform>
                                                <ScaleTransform CenterX="35" CenterY="35"/>
                                            </Ellipse.RenderTransform>
                                        </Ellipse>

                                        <Ellipse x:Name="SpeakingWave3"
                                                Width="70" Height="70"
                                                Stroke="#A855F7"
                                                StrokeThickness="0.8"
                                                Fill="Transparent"
                                                Opacity="0"
                                                Visibility="Collapsed"
                                                Margin="5">
                                            <Ellipse.RenderTransform>
                                                <ScaleTransform CenterX="35" CenterY="35"/>
                                            </Ellipse.RenderTransform>
                                        </Ellipse>

                                        <!-- Microphone Icon - Always Visible -->
                                        <Path x:Name="MicrophoneIcon"
                                              Fill="{StaticResource PrimaryBrush}"
                                              Width="32" Height="32"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Data="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"
                                              Stretch="Uniform">
                                            <Path.RenderTransform>
                                                <ScaleTransform CenterX="16" CenterY="16"/>
                                            </Path.RenderTransform>
                                        </Path>
                                    </Grid>
                                </Canvas>
                            </Grid>
                        </Grid>

                        <!-- Status Footer - Instructions Only -->
                        <Grid Grid.Row="2" Margin="0,40,0,0">
                            <!-- Loading Indicator (shown during initialization) -->
                            <Border x:Name="LoadingPanel"
                                    CornerRadius="8"
                                    Background="#05000000"
                                    Padding="12,6"
                                    Margin="0,0,0,0"
                                    VerticalAlignment="Top"
                                    Visibility="Visible">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <!-- Modern Spinner -->
                                    <Grid Width="16" Height="16" Margin="0,0,8,0">
                                        <Ellipse Fill="Transparent" Stroke="{StaticResource PrimaryBrush}"
                                                StrokeThickness="2" Width="16" Height="16"
                                                StrokeDashArray="3,1">
                                            <Ellipse.RenderTransform>
                                                <RotateTransform x:Name="SpinnerRotate" CenterX="8" CenterY="8"/>
                                            </Ellipse.RenderTransform>
                                            <Ellipse.Triggers>
                                                <EventTrigger RoutedEvent="Loaded">
                                                    <BeginStoryboard>
                                                        <Storyboard RepeatBehavior="Forever">
                                                            <DoubleAnimation Storyboard.TargetName="SpinnerRotate"
                                                                           Storyboard.TargetProperty="Angle"
                                                                           From="0" To="360" Duration="0:0:1"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                            </Ellipse.Triggers>
                                        </Ellipse>
                                    </Grid>
                                    <TextBlock x:Name="LoadingText"
                                              Text="Initializing transcription services..."
                                              FontSize="10"
                                              FontWeight="Medium"
                                              Foreground="{StaticResource PrimaryBrush}"
                                              FontFamily="Segoe UI Variable Text"
                                              VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- Instructions (shown when ready) -->
                            <Border x:Name="InstructionsPanel"
                                    CornerRadius="8"
                                    Background="#05000000"
                                    Padding="12,6"
                                    Margin="0,0,0,0"
                                    VerticalAlignment="Top"
                                    Visibility="Collapsed">
                                <TextBlock Text="Hold Right Alt + speak to dictate"
                                          FontSize="10"
                                          FontWeight="Medium"
                                          Foreground="{StaticResource SubtleBrush}"
                                          FontFamily="Segoe UI Variable Text"
                                          HorizontalAlignment="Center"
                                          TextAlignment="Center"
                                          Opacity="0.7"/>
                            </Border>
                        </Grid>
                    </Grid>
                </Border>
            </Border>
        </Border>
    </Grid>
</Window>
