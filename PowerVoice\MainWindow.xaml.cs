using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Shapes;
using Microsoft.Extensions.Configuration;
using PowerVoice.Extensions;
using PowerVoice.TextInjection;
using PowerVoice.TranscriptionCore;
using WinForms = System.Windows.Forms; // For NotifyIcon

namespace PowerVoice
{
    // Windows API declarations for universal dictation and global hotkeys
    public static class UniversalDictation
    {
        // Queue-based text injection system for reliable character delivery
        private static readonly Queue<TextInjectionRequest> _textQueue = new Queue<TextInjectionRequest>();
        private static readonly object _queueLock = new object();
        private static readonly AutoResetEvent _queueSignal = new AutoResetEvent(false);
        private static Thread? _injectionThread;
        private static volatile bool _isShuttingDown = false;

        // Reuse SmartTextInjector to avoid allocation overhead
        private static readonly SmartTextInjector _smartInjector = new SmartTextInjector();

        // Text injection request
        private sealed class TextInjectionRequest
        {
            public string Text { get; set; } = string.Empty;
            public DateTime Timestamp { get; set; } = DateTime.Now;
        }

        static UniversalDictation()
        {
            StartInjectionThread();
        }

        /// <summary>
        /// Start the dedicated text injection thread
        /// </summary>
        private static void StartInjectionThread()
        {
            _injectionThread = new Thread(InjectionThreadWorker)
            {
                IsBackground = true,
                Name = "TextInjectionWorker"
            };
            _injectionThread.Start();
            DebugT.WriteLine("🚀 Text injection worker thread started");
        }

        /// <summary>
        /// Worker thread that processes text injection requests sequentially
        /// </summary>
        private static void InjectionThreadWorker()
        {
            while (!_isShuttingDown)
            {
                try
                {
                    // Wait for signal that there's work to do
                    _queueSignal.WaitOne();

                    // Process all queued requests
                    while (true)
                    {
                        TextInjectionRequest? request = null;

                        lock (_queueLock)
                        {
                            if (_textQueue.Count == 0)
                                break;
                            request = _textQueue.Dequeue();
                        }

                        if (request != null && !_isShuttingDown)
                        {
                            ProcessTextInjectionRequest(request);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ Text injection worker error: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Process a single text injection request using smart text injection
        /// </summary>
        private static void ProcessTextInjectionRequest(TextInjectionRequest request)
        {
            try
            {
                var windowInfo = GetActiveWindowInfo();
                Debug.WriteLine($"📝 Processing queued text: '{request.Text}'");
                Debug.WriteLine($"📝 Target window: {windowInfo}");

                // Pre-sanitize text
                string sanitizedText = SanitizeTextForKeyboard(request.Text);
                if (string.IsNullOrEmpty(sanitizedText))
                {
                    DebugT.WriteLine("⚠️ No valid characters to send after sanitization");
                    return;
                }

                // REMOVED: Thread.Sleep(5) - this was adding unnecessary 5ms delay!

                // Use the static SmartTextInjector for optimal performance (no allocation overhead)
                var result = _smartInjector.InjectTextAsync(sanitizedText).GetAwaiter().GetResult();

                Debug.WriteLine($"📊 Smart Text Injection Result: {result}");

                if (result.Success)
                {
                    Debug.WriteLine($"✅ Smart injection completed successfully using {result.Method}");
                    Debug.WriteLine($"⚡ Performance: {result.CharactersPerSecond} chars/sec in {result.ElapsedMilliseconds}ms");
                }
                else
                {
                    Debug.WriteLine($"❌ Smart injection failed: {result.Message}");
                    DebugT.WriteLine("⚠️ Falling back to original character-by-character method...");

                    // Fallback to original method if smart injection completely fails
                    bool fallbackSuccess = SendTextUsingKeybdEvent(sanitizedText);
                    if (fallbackSuccess)
                    {
                        Debug.WriteLine($"🐌 FALLBACK: Original character-by-character successful (Length: {sanitizedText.Length})");
                    }
                    else
                    {
                        DebugT.WriteLine("❌ All text injection methods failed");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Error processing text injection request: {ex.Message}");
            }
        }

        /// <summary>
        /// Shutdown the injection thread
        /// </summary>
        public static void Shutdown()
        {
            _isShuttingDown = true;
            _queueSignal.Set();
            _injectionThread?.Join(1000); // Wait up to 1 second
        }

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool AttachThreadInput(uint idAttach, uint idAttachTo, bool fAttach);

        [DllImport("user32.dll")]
        private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

        [DllImport("kernel32.dll")]
        private static extern uint GetCurrentThreadId();

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetWindowText(IntPtr hWnd, char[] text, int count);

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetClassName(IntPtr hWnd, char[] lpClassName, int nMaxCount);

        [DllImport("user32.dll")]
        private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);

        [DllImport("user32.dll")]
        private static extern short VkKeyScan(char ch);

        // Additional Windows API for advanced text injection
        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, string lParam);

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern IntPtr GetFocus();

        [DllImport("user32.dll")]
        private static extern uint SendInput(uint nInputs, INPUT[] pInputs, int cbSize);

        // SendInput structures
        [StructLayout(LayoutKind.Sequential)]
        private struct INPUT
        {
            public uint Type;
            public INPUTUNION Data;
        }

        [StructLayout(LayoutKind.Explicit)]
        private struct INPUTUNION
        {
            [FieldOffset(0)]
            public MOUSEINPUT mi;
            [FieldOffset(0)]
            public KEYBDINPUT ki;
            [FieldOffset(0)]
            public HARDWAREINPUT hi;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct MOUSEINPUT
        {
            public int dx;
            public int dy;
            public uint mouseData;
            public uint dwFlags;
            public uint time;
            public UIntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct KEYBDINPUT
        {
            public ushort wVk;
            public ushort wScan;
            public uint dwFlags;
            public uint time;
            public UIntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct HARDWAREINPUT
        {
            public uint uMsg;
            public ushort wParamL;
            public ushort wParamH;
        }

        // Constants for keybd_event
        private const uint KEYEVENTF_KEYUP = 0x0002;

        public static void SendTextToActiveWindow(string text)
        {
            if (string.IsNullOrEmpty(text)) return;

            // Queue the text injection request for sequential processing
            var request = new TextInjectionRequest
            {
                Text = text,
                Timestamp = DateTime.Now
            };

            lock (_queueLock)
            {
                _textQueue.Enqueue(request);
                Debug.WriteLine($"📋 Queued text injection: '{text}' (Queue size: {_textQueue.Count})");
            }

            // Signal the worker thread that there's work to do
            _queueSignal.Set();
        }

        /// <summary>
        /// Sanitize text to ensure it contains only characters that can be properly sent via keyboard simulation
        /// Optimized for performance - minimal VkKeyScan calls
        /// </summary>
        private static string SanitizeTextForKeyboard(string text)
        {
            if (string.IsNullOrEmpty(text)) return string.Empty;

            // Fast path: most text is already valid
            bool needsSanitization = false;
            foreach (char c in text)
            {
                // Quick check for obviously problematic characters
                if (c < 32 && c != '\t' && c != '\n' && c != '\r')
                {
                    needsSanitization = true;
                    break;
                }
                if (c > 127) // Non-ASCII
                {
                    needsSanitization = true;
                    break;
                }
            }

            // If no sanitization needed, return original
            if (!needsSanitization) return text;

            // Slow path: character-by-character validation
            var result = new System.Text.StringBuilder(text.Length);

            foreach (char c in text)
            {
                // Fast whitespace handling
                if (c == ' ' || c == '\t' || c == '\n' || c == '\r')
                {
                    result.Append(' ');
                    continue;
                }

                // Fast ASCII printable character handling (most common case)
                if (c >= 32 && c <= 126)
                {
                    result.Append(c);
                    continue;
                }

                // Only use VkKeyScan for edge cases
                short vkAndShift = VkKeyScan(c);
                if (vkAndShift != -1)
                {
                    result.Append(c);
                }
                else if (result.Length > 0 && result[result.Length - 1] != ' ')
                {
                    result.Append(' ');
                }
            }

            return result.ToString();
        }

        private static bool SendTextUsingKeybdEvent(string text)
        {
            try
            {
                Debug.WriteLine($"🔧 SendTextUsingKeybdEvent called with: '{text}' (Length: {text.Length})");

                // Get target window info for selective modifier key clearing
                var windowInfo = GetActiveWindowInfo();

                // CRITICAL: Clear modifier keys only for external applications
                // This prevents Alt key interference without disrupting PowerVoice's own operations
                ClearAllModifierKeys(windowInfo);

                // Give applications time to process modifier key releases
                bool isExternalApp = !windowInfo.Contains("PowerVoice", StringComparison.OrdinalIgnoreCase);
                Thread.Sleep(isExternalApp ? 2 : 1);

                // Check if we need to use chunked delivery for buffer-limited applications
                if (NeedsChunkedDelivery(windowInfo, text.Length))
                {
                    return SendTextInChunks(text, windowInfo);
                }

                // Research-based balanced timing for guaranteed delivery with good performance
                // PowerToys KeyDelay: LONG_PRESS_DELAY_MILLIS = 900, ON_HOLD_WAIT_TIMEOUT_MILLIS = 50
                // ZoomIt DemoType: NOTEPAD_REFRESH for keydown/up latency, especially for Notepad
                // PowerAccent: Thread.Sleep(1) for Terminal compatibility - using moderate values
                bool isNotepad = windowInfo.ToLowerInvariant().Contains("notepad");
                int modifierDelay = isNotepad ? 8 : 1;      // Balanced modifier delays (down from 10ms for Notepad)
                int keyPressDelay = isNotepad ? 12 : 2;     // Balanced key press delay (down from 15ms for Notepad)
                int betweenCharDelay = isNotepad ? 20 : 3;  // Balanced delay between characters (down from 25ms for Notepad)

                int successCount = 0;
                int failCount = 0;
                int charIndex = 0;

                foreach (char c in text)
                {
                    charIndex++;
                    try
                    {
                        Debug.WriteLine($"🔤 Processing character {charIndex}/{text.Length}: '{c}' (U+{((int)c):X4})");

                        // Skip control characters and characters that are likely to cause issues
                        if (char.IsControl(c) && c != '\r' && c != '\n' && c != '\t')
                        {
                            Debug.WriteLine($"⚠️ Skipping control character: '{c}' (U+{((int)c):X4})");
                            continue;
                        }

                        // Convert newlines and tabs to spaces for better compatibility
                        char charToSend = c;
                        if (c == '\r' || c == '\n' || c == '\t')
                        {
                            charToSend = ' ';
                            Debug.WriteLine($"🔄 Converted '{c}' to space");
                        }

                        // Get virtual key code for the character
                        short vkAndShift = VkKeyScan(charToSend);

                        if (vkAndShift == -1)
                        {
                            // Character cannot be mapped, replace with space
                            Debug.WriteLine($"⚠️ Cannot map character '{charToSend}' (U+{((int)charToSend):X4}), using space");
                            vkAndShift = VkKeyScan(' '); // Use space instead
                        }

                        // If it's still unmappable, skip it entirely
                        if (vkAndShift == -1)
                        {
                            Debug.WriteLine($"❌ Space character unmappable, skipping");
                            continue;
                        }

                        byte vk = (byte)(vkAndShift & 0xFF);
                        byte shiftState = (byte)((vkAndShift >> 8) & 0xFF);

                        // Handle Shift key requirement
                        bool needsShift = (shiftState & 1) != 0;

                        Debug.WriteLine($"🎯 About to send character '{charToSend}' (VK: {vk:X2}, Shift: {needsShift})");

                        if (needsShift)
                        {
                            keybd_event(0x10, 0, 0, UIntPtr.Zero); // VK_SHIFT down
                            if (modifierDelay > 0) Thread.Sleep(modifierDelay);
                        }

                        // Send the actual key with ultra-fast timing
                        keybd_event(vk, 0, 0, UIntPtr.Zero); // Key down
                        if (keyPressDelay > 0) Thread.Sleep(keyPressDelay);
                        keybd_event(vk, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // Key up

                        // Release Shift if it was pressed
                        if (needsShift)
                        {
                            if (modifierDelay > 0) Thread.Sleep(modifierDelay);
                            keybd_event(0x10, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_SHIFT up
                        }

                        successCount++;
                        Debug.WriteLine($"✅ Successfully sent character '{charToSend}' ({charIndex}/{text.Length})");

                        // Ultra-fast between character timing for most apps, slower for Notepad
                        if (betweenCharDelay > 0) Thread.Sleep(betweenCharDelay);

                        // For Notepad, add extra delay every few characters to prevent buffer overflow
                        // Research-based approach: PowerToys ON_HOLD_WAIT_TIMEOUT_MILLIS = 50ms intervals
                        if (isNotepad && charIndex % 8 == 0)
                        {
                            Thread.Sleep(40); // Balanced timing (down from 50ms)
                            Debug.WriteLine($"🐌 Notepad buffer break after {charIndex} characters");
                        }
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        Debug.WriteLine($"❌ Failed to send character '{c}' at position {charIndex}: {ex.Message}");
                    }
                }

                Debug.WriteLine($"📊 keybd_event results: {successCount} success, {failCount} failed out of {text.Length} characters");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ SendTextUsingKeybdEvent error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Determine if the application needs chunked text delivery due to buffer limitations
        /// Based on Microsoft PowerToys research and empirical testing
        /// </summary>
        private static bool NeedsChunkedDelivery(string windowInfo, int textLength)
        {
            // Convert to lowercase for case-insensitive comparison
            string lowerWindowInfo = windowInfo.ToLowerInvariant();

            // Applications with known buffer limitations based on Microsoft PowerToys research
            string[] bufferLimitedApps = {
                "notepad.exe",           // Basic Notepad has very small input buffer (PowerToys ZoomIt findings)
                "cmd.exe",               // Command prompt - limited input processing
                "powershell.exe",        // PowerShell - limited input processing
                "calculator.exe",        // Calculator - very limited text input
                "mspaint.exe",           // Paint - minimal text processing capability
                "wordpad.exe"            // WordPad - moderate buffer limitations
            };

            // Check if it's a buffer-limited app
            bool isBufferLimited = bufferLimitedApps.Any(lowerWindowInfo.Contains);

            // Also check for specific window classes known to have buffer issues
            if (lowerWindowInfo.Contains("class: notepad") ||
                lowerWindowInfo.Contains("class: consolewindowclass") ||
                lowerWindowInfo.Contains("class: cabinetwclass"))
            {
                isBufferLimited = true;
            }

            // Research-based conservative thresholds - balanced approach using Microsoft findings
            // PowerToys ZoomIt: Uses small chunks for Notepad, but not extremely small
            // Microsoft Terminal: Uses Thread.Sleep(1) for compatibility
            int chunkThreshold = 120; // Moderately conservative default (down from 200, up from 150)

            if (lowerWindowInfo.Contains("notepad.exe") || lowerWindowInfo.Contains("class: notepad"))
            {
                chunkThreshold = 12; // Conservative for Notepad but not extreme (up from 8, down from 15)
            }
            else if (isBufferLimited)
            {
                chunkThreshold = 25; // Balanced for other buffer-limited apps (up from 20, down from 30)
            }

            bool needsChunking = textLength > chunkThreshold;

            if (needsChunking)
            {
                Debug.WriteLine($"🔄 Text length {textLength} exceeds research-based threshold {chunkThreshold} for {windowInfo} - using chunked delivery");
            }

            return needsChunking;
        }

        /// <summary>
        /// Send text in smaller chunks to prevent buffer overflow in applications with limited input buffers
        /// </summary>
        private static bool SendTextInChunks(string text, string windowInfo)
        {
            try
            {
                Debug.WriteLine($"📦 SendTextInChunks called for: '{text}' (Length: {text.Length})");

                // Determine optimal chunk size based on application type
                int chunkSize = GetOptimalChunkSize(windowInfo);
                int chunkDelay = GetOptimalChunkDelay(windowInfo);

                Debug.WriteLine($"📦 Using chunk size: {chunkSize}, delay: {chunkDelay}ms");

                int totalChunks = (int)Math.Ceiling((double)text.Length / chunkSize);
                int successfulChunks = 0;

                for (int i = 0; i < text.Length; i += chunkSize)
                {
                    int currentChunkSize = Math.Min(chunkSize, text.Length - i);
                    string chunk = text.Substring(i, currentChunkSize);
                    int chunkNumber = (i / chunkSize) + 1;

                    Debug.WriteLine($"📦 Sending chunk {chunkNumber}/{totalChunks}: '{chunk}' (Length: {chunk.Length})");

                    // Send this chunk using the normal character-by-character method
                    bool chunkSuccess = SendSingleChunk(chunk);

                    if (chunkSuccess)
                    {
                        successfulChunks++;
                        Debug.WriteLine($"✅ Chunk {chunkNumber}/{totalChunks} sent successfully");
                    }
                    else
                    {
                        Debug.WriteLine($"❌ Chunk {chunkNumber}/{totalChunks} failed");
                    }

                    // Add delay between chunks to let the application process
                    if (i + chunkSize < text.Length && chunkDelay > 0)
                    {
                        Thread.Sleep(chunkDelay);
                    }
                }

                bool overallSuccess = successfulChunks == totalChunks;
                Debug.WriteLine($"📊 Chunked delivery result: {successfulChunks}/{totalChunks} chunks successful");

                return overallSuccess;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ SendTextInChunks error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get optimal chunk size for the target application based on Microsoft PowerToys research
        /// </summary>
        private static int GetOptimalChunkSize(string windowInfo)
        {
            string lowerWindowInfo = windowInfo.ToLowerInvariant();

            // Research-based balanced chunk sizes from Microsoft PowerToys findings
            // ZoomIt uses small chunks for Notepad, but we'll use moderate sizes for better performance
            if (lowerWindowInfo.Contains("notepad.exe") || lowerWindowInfo.Contains("class: notepad"))
            {
                return 8; // Balanced for Notepad (up from 6, still conservative)
            }

            if (lowerWindowInfo.Contains("cmd.exe") || lowerWindowInfo.Contains("powershell.exe"))
            {
                return 15; // Moderately conservative for command line (up from 12)
            }

            if (lowerWindowInfo.Contains("calculator.exe") || lowerWindowInfo.Contains("mspaint.exe"))
            {
                return 10; // Balanced for apps with minimal text input (up from 8)
            }

            // Default balanced chunk size for other applications
            return 35; // Balanced approach (up from 25, down from 50)
        }

        /// <summary>
        /// Get optimal delay between chunks for the target application
        /// Based on Microsoft PowerToys research and Windows API best practices
        /// </summary>
        private static int GetOptimalChunkDelay(string windowInfo)
        {
            string lowerWindowInfo = windowInfo.ToLowerInvariant();

            // Research-based balanced delays from Microsoft PowerToys findings
            // PowerToys KeyDelay: ON_HOLD_WAIT_TIMEOUT_MILLIS = 50
            // ZoomIt: CLIPBOARD_REFRESH for processing time - using moderate values
            if (lowerWindowInfo.Contains("notepad.exe") || lowerWindowInfo.Contains("class: notepad"))
            {
                return 100; // Balanced delay for Notepad (down from 150ms, still safe)
            }

            if (lowerWindowInfo.Contains("cmd.exe") || lowerWindowInfo.Contains("powershell.exe"))
            {
                return 60; // Moderate delay for command line (down from 75ms)
            }

            if (lowerWindowInfo.Contains("calculator.exe") || lowerWindowInfo.Contains("mspaint.exe"))
            {
                return 75; // Balanced delay for minimal text input apps (down from 100ms)
            }

            // Default moderate delay for other applications
            return 40; // Balanced approach (down from 50ms, up from 20ms)
        }

        /// <summary>
        /// Send a single chunk of text using character-by-character method
        /// Based on Microsoft PowerToys research for maximum reliability
        /// </summary>
        private static bool SendSingleChunk(string chunk)
        {
            try
            {
                // Use research-based balanced timing for chunk delivery
                // PowerToys KeyDelay: LONG_PRESS_DELAY_MILLIS = 900, ON_HOLD_WAIT_TIMEOUT_MILLIS = 50
                // ZoomIt DemoType: NOTEPAD_REFRESH for keydown/up latency
                // PowerAccent: Thread.Sleep(1) for Terminal compatibility - using moderate values
                int modifierDelay = 8;       // Balanced modifier key delays (down from 10ms)
                int keyPressDelay = 12;      // Balanced key press delay (down from 15ms)
                int betweenCharDelay = 20;   // Balanced delay between characters (down from 25ms)

                int successCount = 0;
                int failCount = 0;
                int charIndex = 0;

                foreach (char c in chunk)
                {
                    charIndex++;
                    try
                    {
                        // Skip control characters and characters that are likely to cause issues
                        if (char.IsControl(c) && c != '\r' && c != '\n' && c != '\t')
                        {
                            continue;
                        }

                        // Convert newlines and tabs to spaces for better compatibility
                        char charToSend = c;
                        if (c == '\r' || c == '\n' || c == '\t')
                        {
                            charToSend = ' ';
                        }

                        // Get virtual key code for the character
                        short vkAndShift = VkKeyScan(charToSend);

                        if (vkAndShift == -1)
                        {
                            // Character cannot be mapped, replace with space
                            vkAndShift = VkKeyScan(' ');
                            charToSend = ' ';
                        }

                        // If it's still unmappable, skip it entirely
                        if (vkAndShift == -1)
                        {
                            continue;
                        }

                        byte vk = (byte)(vkAndShift & 0xFF);
                        byte shiftState = (byte)((vkAndShift >> 8) & 0xFF);

                        // Handle Shift key requirement
                        bool needsShift = (shiftState & 1) != 0;

                        if (needsShift)
                        {
                            keybd_event(0x10, 0, 0, UIntPtr.Zero); // VK_SHIFT down
                            if (modifierDelay > 0) Thread.Sleep(modifierDelay);
                        }

                        // Send the actual key
                        keybd_event(vk, 0, 0, UIntPtr.Zero); // Key down
                        if (keyPressDelay > 0) Thread.Sleep(keyPressDelay);
                        keybd_event(vk, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // Key up

                        // Release Shift if it was pressed
                        if (needsShift)
                        {
                            if (modifierDelay > 0) Thread.Sleep(modifierDelay);
                            keybd_event(0x10, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_SHIFT up
                        }

                        successCount++;

                        // Conservative between character timing for chunks
                        if (betweenCharDelay > 0) Thread.Sleep(betweenCharDelay);

                        // Research-based buffer relief: Add extra delay every few characters
                        // PowerToys ON_HOLD_WAIT_TIMEOUT_MILLIS = 50ms for processing intervals
                        if (charIndex % 5 == 0)
                        {
                            Thread.Sleep(40); // Balanced timing (down from 50ms)
                        }
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        Debug.WriteLine($"❌ Failed to send character '{c}' in chunk: {ex.Message}");
                    }
                }

                Debug.WriteLine($"📊 Chunk delivery: {successCount} success, {failCount} failed out of {chunk.Length} characters");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ SendSingleChunk error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Clear all modifier keys to prevent interference during text injection
        /// Only clear keys when sending to external applications, not PowerVoice itself
        /// Avoids clearing Right Alt immediately after PowerVoice usage to prevent focus issues
        /// </summary>
        private static void ClearAllModifierKeys(string targetWindowInfo)
        {
            try
            {
                // Don't clear modifier keys if we're sending to PowerVoice itself
                // or if the target window info suggests it's our own application
                if (targetWindowInfo.Contains("PowerVoice", StringComparison.OrdinalIgnoreCase))
                {
                    DebugT.WriteLine("🔧 Skipping modifier key clearing - target is PowerVoice");
                    return;
                }

                // Release all possible modifier keys that might be pressed
                keybd_event(0x10, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_SHIFT
                keybd_event(0x11, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_CONTROL
                keybd_event(0x12, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_MENU (Alt)
                keybd_event(0xA0, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_LSHIFT
                keybd_event(0xA1, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_RSHIFT
                keybd_event(0xA2, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_LCONTROL
                keybd_event(0xA3, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_RCONTROL
                keybd_event(0xA4, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_LMENU (Left Alt)
                keybd_event(0xA5, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_RMENU (Right Alt)

                // Ultra-minimal delay to ensure modifier keys are released
                // No delay for maximum speed

                DebugT.WriteLine("🔧 Modifier keys cleared for external application");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Error clearing modifier keys: {ex.Message}");
            }
        }

        // Flag to prevent infinite loops when clearing Alt key state
        private static volatile bool _isInAltClearingProcess = false;

        /// <summary>
        /// Public property to check if Alt clearing is in progress (prevents infinite loops)
        /// </summary>
        public static bool IsInAltClearingProcess => _isInAltClearingProcess;

        /// <summary>
        /// Immediately clear the Alt key state to prevent menu activation in target applications
        /// This is called as soon as PowerVoice detects Alt press to prevent external apps from seeing it
        /// </summary>
        public static void ClearAltKeyImmediately()
        {
            // Prevent infinite recursion from hook catching our own key events
            if (_isInAltClearingProcess) return;

            try
            {
                _isInAltClearingProcess = true;

                // REFINED Alt clearing to prevent double menu activation

                // STEP 1: Release all Alt key variants
                keybd_event(0x12, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_MENU (Alt)
                keybd_event(0xA4, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_LMENU (Left Alt)
                keybd_event(0xA5, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_RMENU (Right Alt)

                // STEP 2: Brief delay for processing
                System.Threading.Thread.Sleep(1);

                // STEP 3: Send ESC to cancel any menu activation
                keybd_event(0x1B, 0, 0, UIntPtr.Zero);               // VK_ESCAPE down
                keybd_event(0x1B, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_ESCAPE up

                // STEP 4: Final Alt release to ensure clearing
                keybd_event(0xA5, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_RMENU (Right Alt)

                DebugT.WriteLine("🔧 Alt key state cleared to prevent menu activation");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Error clearing Alt key immediately: {ex.Message}");
            }
            finally
            {
                _isInAltClearingProcess = false;
            }
        }

        public static string GetActiveWindowInfo()
        {
            try
            {
                IntPtr activeWindow = GetForegroundWindow();
                if (activeWindow == IntPtr.Zero) return "No active window";

                char[] windowText = new char[256];
                char[] className = new char[256];

                _ = GetWindowText(activeWindow, windowText, windowText.Length);
                _ = GetClassName(activeWindow, className, className.Length);

                string windowTitle = new string(windowText).TrimEnd('\0');
                string classNameStr = new string(className).TrimEnd('\0');

                return $"Window: {windowTitle} (Class: {classNameStr})";
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}";
            }
        }
    }

    // Global keyboard hook for detecting ALL keys
    public static class GlobalKeyboardHook
    {
        // Keyboard hook constants
        private const int WH_KEYBOARD_LL = 13;
        private const int WM_KEYDOWN = 0x0100;
        private const int WM_KEYUP = 0x0101;
        private const int WM_SYSKEYDOWN = 0x0104;
        private const int WM_SYSKEYUP = 0x0105;

        // Hook procedure delegate
        public delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);

        // Windows API declarations
        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr GetModuleHandle([MarshalAs(UnmanagedType.LPTStr)] string? lpModuleName);

        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        // Hook handle and procedure with explicit lifetime management
        private static IntPtr _hookID = IntPtr.Zero;
        private static LowLevelKeyboardProc _proc = HookCallback;
        private static GCHandle _procHandle; // Explicit GC protection for delegate

        // Events for key press detection
        public static event EventHandler<KeyPressedEventArgs>? KeyPressed;

        // Custom event args for key information
        public class KeyPressedEventArgs : EventArgs
        {
            public int VirtualKeyCode { get; set; }
            public string KeyName { get; set; } = string.Empty;
            public bool IsKeyDown { get; set; }
            public string EventType { get; set; } = string.Empty;
        }

        public static void StartHook()
        {
            // Pin the delegate to prevent garbage collection
            if (!_procHandle.IsAllocated)
            {
                _procHandle = GCHandle.Alloc(_proc, GCHandleType.Normal);
            }

            _hookID = SetHook(_proc);

            // Force GC test to verify delegate survival
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            // Keep delegate explicitly alive
            GC.KeepAlive(_proc);
        }

        public static void StopHook()
        {
            if (_hookID != IntPtr.Zero)
            {
                UnhookWindowsHookEx(_hookID);
                _hookID = IntPtr.Zero;
            }

            // Free the pinned delegate handle
            if (_procHandle.IsAllocated)
            {
                _procHandle.Free();
            }
        }

        private static IntPtr SetHook(LowLevelKeyboardProc proc)
        {
            using (var curProcess = Process.GetCurrentProcess())
            using (var curModule = curProcess.MainModule)
            {
                // Enhanced logging for delegate lifetime debugging
                DebugT.WriteLine("🔧 INSTALLING HOOK WITH ENHANCED DELEGATE MANAGEMENT");

                // Try using GetModuleHandle(null) for more reliable hook installation
                var moduleHandle = GetModuleHandle(null);
                var hookId = SetWindowsHookEx(WH_KEYBOARD_LL, proc, moduleHandle, 0);

                // Get last Win32 error for detailed debugging
                var lastError = Marshal.GetLastWin32Error();
                var threadId = System.Threading.Thread.CurrentThread.ManagedThreadId;

                // If hook failed, try alternative approach
                if (hookId == IntPtr.Zero)
                {
                    try
                    {
                        var fallbackHookId = SetWindowsHookEx(WH_KEYBOARD_LL, proc,
                            GetModuleHandle(curModule?.ModuleName), 0);

                        var fallbackError = Marshal.GetLastWin32Error();

                        return fallbackHookId;
                    }
                    catch { /* Ignore fallback errors */ }
                }

                // Add a test log entry immediately after hook installation to verify callback works
                if (hookId != IntPtr.Zero)
                {
                    DebugT.WriteLine("✅ HOOK INSTALLED & DELEGATE PINNED - Ready for callbacks");
                }
                else
                {
                    DebugT.WriteLine($"❌ HOOK INSTALLATION FAILED - LastError={lastError}");
                }

                return hookId;
            }
        }

        private static IntPtr HookCallback(int nCode, IntPtr wParam, IntPtr lParam)
        {
            if (nCode >= 0)
            {
                // Read the virtual key code from the low-level input structure
                int vkCode = Marshal.ReadInt32(lParam);

                // CRITICAL: Intercept and consume ONLY RIGHT Alt key to prevent menu activation
                // LEFT Alt key will be allowed to pass through normally
                if (vkCode == 0xA5) // VK_RMENU (Right Alt only)
                {
                    // Determine event type for Right Alt key - handle ALL possible message types
                    bool altKeyDown = false;
                    string altEventType = "";
                    string altSide = "Right"; // Only Right Alt now

                    // Handle all possible keyboard message types for Right Alt key
                    if (wParam == (IntPtr)WM_KEYDOWN)
                    {
                        altKeyDown = true;
                        altEventType = "KeyDown";
                    }
                    else if (wParam == (IntPtr)WM_KEYUP)
                    {
                        altKeyDown = false;
                        altEventType = "KeyUp";
                    }
                    else if (wParam == (IntPtr)WM_SYSKEYDOWN)
                    {
                        altKeyDown = true;
                        altEventType = "SysKeyDown";
                    }
                    else if (wParam == (IntPtr)WM_SYSKEYUP)
                    {
                        altKeyDown = false;
                        altEventType = "SysKeyUp";
                    }
                    else
                    {
                        // For any other Alt message type, assume key up
                        altKeyDown = false;
                        altEventType = $"Unknown_0x{wParam:X}";
                    }

                    // DEBUG: Track Right Alt events we're intercepting
                    string logMessage = $"🔑 {altSide.ToUpper()} ALT INTERCEPTED: {altEventType} (wParam=0x{wParam:X}, vkCode=0x{vkCode:X}) - CONSUMED";

                    // Fire the event for PowerVoice's internal handling
                    KeyPressed?.Invoke(null, new KeyPressedEventArgs
                    {
                        VirtualKeyCode = vkCode,
                        KeyName = $"{altSide} Alt",
                        IsKeyDown = altKeyDown,
                        EventType = altEventType
                    });

                    // IMMEDIATE Alt state clearing ONLY on key RELEASE to prevent menu activation
                    // Don't clear on press as it causes double menu activation
                    if (!altKeyDown && !UniversalDictation.IsInAltClearingProcess) // Key released and not already clearing
                    {
                        // Clear Alt state immediately when released to prevent menu activation
                        Task.Run(() => UniversalDictation.ClearAltKeyImmediately());
                    }

                    // CONSUME ONLY Right Alt events - do NOT pass to other applications
                    // Left Alt will pass through normally for standard menu access
                    // Return 1 to indicate the message has been processed and should NOT be passed on
                    return (IntPtr)1;
                }

                // ADDITIONAL: Intercept F10 key which can also activate Alt menus
                // This prevents F10 from activating menus when Right Alt is being used for voice
                if (vkCode == 0x79) // VK_F10
                {
                    // Check if we're currently in a voice recording session
                    // If so, consume F10 to prevent menu activation
                    bool isRecording = GetCurrentRecordingState();
                    if (isRecording)
                    {
                        return (IntPtr)1; // Consume F10 during recording
                    }
                }

                // For all other keys, determine event type and pass through normally
                bool isKeyDown = false;
                string eventType = "";
                if (wParam == (IntPtr)WM_KEYDOWN)
                {
                    isKeyDown = true;
                    eventType = "KeyDown";
                }
                else if (wParam == (IntPtr)WM_KEYUP)
                {
                    isKeyDown = false;
                    eventType = "KeyUp";
                }
                else if (wParam == (IntPtr)WM_SYSKEYDOWN)
                {
                    isKeyDown = true;
                    eventType = "SysKeyDown";
                }
                else if (wParam == (IntPtr)WM_SYSKEYUP)
                {
                    isKeyDown = false;
                    eventType = "SysKeyUp";
                }

                // Get readable key name for non-Right Alt keys
                string keyName = GetKeyName(vkCode);

                // Fire the event with key information for non-Right Alt keys
                KeyPressed?.Invoke(null, new KeyPressedEventArgs
                {
                    VirtualKeyCode = vkCode,
                    KeyName = keyName,
                    IsKeyDown = isKeyDown,
                    EventType = eventType
                });
            }

            // Pass all non-Right Alt events to other applications normally
            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }

        /// <summary>
        /// Helper method to check if we're currently recording
        /// </summary>
        private static bool GetCurrentRecordingState()
        {
            // Simple implementation - return false for now since F10 blocking during recording
            // is an optional enhancement. The main Right Alt blocking will still work.
            return false;
        }

        private static string GetKeyName(int vkCode)
        {
            // Convert virtual key code to readable name
            return vkCode switch
            {
                0x08 => "Backspace",
                0x09 => "Tab",
                0x0D => "Enter",
                0x10 => "Shift",
                0x11 => "Ctrl",
                0x12 => "Alt",
                0x1B => "Escape",
                0x20 => "Space",
                0x21 => "Page Up",
                0x22 => "Page Down",
                0x23 => "End",
                0x24 => "Home",
                0x25 => "Left Arrow",
                0x26 => "Up Arrow",
                0x27 => "Right Arrow",
                0x28 => "Down Arrow",
                0x2E => "Delete",
                0xA0 => "Left Shift",
                0xA1 => "Right Shift",
                0xA2 => "Left Ctrl",
                0xA3 => "Right Ctrl",
                0xA4 => "Left Alt",
                0xA5 => "Right Alt",
                >= 0x30 and <= 0x39 => ((char)vkCode).ToString(), // Numbers 0-9
                >= 0x41 and <= 0x5A => ((char)vkCode).ToString(), // Letters A-Z
                >= 0x60 and <= 0x69 => $"Numpad {(char)(vkCode - 0x60 + '0')}", // Numpad 0-9
                >= 0x70 and <= 0x87 => $"F{vkCode - 0x6F}", // Function keys F1-F24
                _ => $"Key_{vkCode:X2}"
            };
        }
    }

    public partial class MainWindow : Window, IDisposable
    {
        // Import VkKeyScan for character validation
        [DllImport("user32.dll")]
        private static extern short VkKeyScan(char ch);

        private TranscriptionServiceFactory? _transcriptionServiceFactory;
        private ILiveTranscriptionService? _liveTranscriptionService;
        private bool _isRightAltPressed; // Track Right Alt key state
        private bool _isRecording; // Track recording state
        private Storyboard? _microphonePulse;
        private Storyboard? _listeningWave;
        private Storyboard? _speakingAnimation; // New speaking animation
        private WinForms.NotifyIcon? _notifyIcon; // System tray icon
        private WinForms.ContextMenuStrip? _contextMenu; // Context menu reference for updates
        private bool _isExiting = false; // Track if we're explicitly exiting

        // Text accumulation queue for multiple transcription segments during one Alt session
        private readonly Queue<string> _textSegmentQueue = new Queue<string>();
        private readonly object _textQueueLock = new object();

        // Status message constants
        private const string STATUS_READY = "Ready";
        private const string STATUS_LISTENING = "Listening";
        private const string STATUS_PROCESSING = "Processing";
        private const string STATUS_SPEAKING = "Speaking";
        private const string STATUS_INITIALIZING = "Initializing";

        /// <summary>
        /// Check if UI updates should be performed based on window visibility state.
        /// Returns false when window is minimized to optimize performance for 24/7 background operation.
        /// </summary>
        private bool ShouldUpdateUI => WindowState != WindowState.Minimized && IsVisible;

        /// <summary>
        /// Optimized method to start listening UI state (status + animation) - skips updates when minimized
        /// </summary>
        private void StartListeningUI()
        {
            if (!ShouldUpdateUI)
            {
                DebugT.WriteLine("📱 Skipping listening UI updates (minimized)");
                return;
            }

            UpdateStatus(STATUS_LISTENING);
            StartListeningAnimation();
        }

        /// <summary>
        /// Optimized method to stop listening UI state (status + animation) - skips updates when minimized
        /// </summary>
        private void StopListeningUI()
        {
            if (!ShouldUpdateUI)
            {
                DebugT.WriteLine("📱 Skipping stop listening UI updates (minimized)");
                return;
            }

            UpdateStatus(STATUS_PROCESSING);
            StopListeningAnimation();
            UpdateStatus(STATUS_READY);
        }

        /// <summary>
        /// Show the loading state with a custom message
        /// </summary>
        /// <param name="message">Loading message to display</param>
        private void ShowLoadingState(string message)
        {
            try
            {
                if (LoadingPanel != null)
                {
                    LoadingPanel.Visibility = Visibility.Visible;
                }

                if (LoadingText != null)
                {
                    LoadingText.Text = message;
                }

                // Hide instructions while loading
                if (InstructionsPanel != null)
                {
                    InstructionsPanel.Visibility = Visibility.Collapsed;
                }

                Debug.WriteLine($"🔄 Loading state shown: {message}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Error showing loading state: {ex.Message}");
            }
        }

        /// <summary>
        /// Update the loading message text
        /// </summary>
        /// <param name="message">New loading message</param>
        private void UpdateLoadingText(string message)
        {
            try
            {
                if (LoadingText != null)
                {
                    LoadingText.Text = message;
                }

                DebugT.WriteLine($"🔄 Loading text updated: {message}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error updating loading text: {ex.Message}");
            }
        }

        /// <summary>
        /// Hide the loading state and show the ready UI
        /// </summary>
        private void HideLoadingState()
        {
            try
            {
                if (LoadingPanel != null)
                {
                    LoadingPanel.Visibility = Visibility.Collapsed;
                }

                DebugT.WriteLine("✅ Loading state hidden");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error hiding loading state: {ex.Message}");
            }
        }

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;

            // Initialize basic UI components immediately
            InitializeAnimations();
            InitializeSystemTray();
            InitializeGlobalHotkey();

            // Setup always-on-top behavior that respects minimized state
            InitializeAlwaysOnTopBehavior();

            // Show loading state immediately - this will be visible by default from XAML
            ShowLoadingState("Initializing transcription services...");

            // Start async initialization immediately without blocking UI
            _ = InitializeAsync();
        }

        /// <summary>
        /// Async initialization method that loads the Live Transcription Service without blocking the UI
        /// </summary>
        private async Task InitializeAsync()
        {
            try
            {
                DebugT.WriteLine("🔴 Starting async Live Transcription Service initialization...");

                // Small delay to ensure UI is rendered first
                await Task.Delay(50);

                // Update loading state
                await Dispatcher.InvokeAsync(() =>
                    UpdateLoadingText("Loading configuration..."));

                // Load configuration
                var configFilePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
                var configurationBuilder = new ConfigurationBuilder()
                    .AddJsonFile(configFilePath, optional: false, reloadOnChange: true);
                var configuration = configurationBuilder.Build();

                // Update loading state
                await Dispatcher.InvokeAsync(() =>
                    UpdateLoadingText("Creating transcription service..."));

                // Small delay to show progress
                await Task.Delay(100);

                // Initialize transcription service using factory
                _transcriptionServiceFactory = new TranscriptionServiceFactory(configuration);
                _liveTranscriptionService = _transcriptionServiceFactory.CreateLiveTranscriptionService();

                // Subscribe to transcription service events
                _liveTranscriptionService.TranscriptionReceived += OnTranscriptionReceived;
                _liveTranscriptionService.ErrorOccurred += OnErrorOccurred;
                _liveTranscriptionService.SessionStarted += OnSessionStarted;
                _liveTranscriptionService.SessionEnded += OnSessionEnded;
                _liveTranscriptionService.SpeechStarted += OnSpeechStarted;
                _liveTranscriptionService.SpeechStopped += OnSpeechStopped;

                // Subscribe to immediate voice detection events for instant speaking animation
                _liveTranscriptionService.VoiceDetected += OnVoiceDetectedImmediate;
                _liveTranscriptionService.VoiceStopped += OnVoiceStoppedImmediate;

                DebugT.WriteLine("✅ Live Transcription Service created successfully");

                // Update loading state
                await Dispatcher.InvokeAsync(() =>
                    UpdateLoadingText("Pre-initializing speech components..."));

                // PRE-INITIALIZE the service for instant responsiveness when Alt is pressed
                // This pre-caches all components that don't consume credits
                DebugT.WriteLine("🚀 Pre-initializing Live Speech components for instant response...");
                await _liveTranscriptionService.PreInitializeAsync();
                DebugT.WriteLine("✅ Live Speech pre-initialization completed - ready for instant activation");

                // Small delay before showing ready state
                await Task.Delay(200);

                // Hide loading state and show ready state
                await Dispatcher.InvokeAsync(() =>
                {
                    HideLoadingState();
                    UpdateStatus(STATUS_READY);

                    // Initialize UI for current mode
                    var currentMode = GetTranscriptionMode();
                    UpdateUIForMode(currentMode);
                });

                DebugT.WriteLine("✅ Application initialization completed successfully");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Failed to initialize Live Transcription Service: {ex.Message}");

                await Dispatcher.InvokeAsync(() =>
                {
                    HideLoadingState();
                    UpdateStatus("⚠️ Service initialization failed");
                    System.Windows.MessageBox.Show($"Failed to initialize application: {ex.Message}", "Initialization Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }

        /// <summary>
        /// Get the current transcription mode from configuration
        /// </summary>
        /// <returns>Transcription mode string</returns>
        private static string GetTranscriptionMode()
        {
            try
            {
                var builder = new ConfigurationBuilder()
                    .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

                var configuration = builder.Build();
                return configuration["AzureSpeech:TranscriptionMode"] ?? "FastTranscription";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Error reading transcription mode: {ex.Message}");
                return "FastTranscription"; // Default fallback
            }
        }

        /// <summary>
        /// Update context menu mode selection checkmarks
        /// </summary>
        /// <param name="contextMenu">Context menu to update</param>
        private void UpdateContextMenuModeSelection(WinForms.ContextMenuStrip contextMenu)
        {
            try
            {
                var currentMode = GetTranscriptionMode();
                Debug.WriteLine($"🔄 UpdateContextMenuModeSelection: Current mode is '{currentMode}'");

                // Find the transcription mode menu item
                foreach (WinForms.ToolStripItem item in _contextMenu.Items)
                {
                    if (item is WinForms.ToolStripMenuItem menuItem && menuItem.Text == "Transcription Mode")
                    {
                        Debug.WriteLine($"✅ Found Transcription Mode menu item with {menuItem.DropDownItems.Count} sub-items");

                        // Update checkmarks for submenu items
                        foreach (WinForms.ToolStripItem subItem in menuItem.DropDownItems)
                        {
                            if (subItem is WinForms.ToolStripMenuItem subMenuItem)
                            {
                                bool shouldBeChecked = false;
                                if (subMenuItem.Text == "Fast Transcription" || subMenuItem.Text == "Fast")
                                {
                                    shouldBeChecked = currentMode == "FastTranscription";
                                    Debug.WriteLine($"🎯 Fast Transcription: mode='{currentMode}', checked={shouldBeChecked}");
                                }
                                else if (subMenuItem.Text == "Real-time")
                                {
                                    shouldBeChecked = currentMode == "FastTranscription";
                                    Debug.WriteLine($"🎯 Real-time: mode='{currentMode}', checked={shouldBeChecked}");
                                }
                                else if (subMenuItem.Text == "Whisper" || subMenuItem.Text == "Whisper (Legacy)")
                                {
                                    shouldBeChecked = currentMode == "Whisper";
                                    Debug.WriteLine($"🎯 Whisper (Legacy): mode='{currentMode}', checked={shouldBeChecked}");
                                }
                                else if (subMenuItem.Text == "Whisper Tiny")
                                {
                                    shouldBeChecked = currentMode == "WhisperTiny";
                                    Debug.WriteLine($"🎯 Whisper Tiny: mode='{currentMode}', checked={shouldBeChecked}");
                                }
                                else if (subMenuItem.Text == "Whisper Base")
                                {
                                    shouldBeChecked = currentMode == "WhisperBase";
                                    Debug.WriteLine($"🎯 Whisper Base: mode='{currentMode}', checked={shouldBeChecked}");
                                }
                                else if (subMenuItem.Text == "Whisper Small")
                                {
                                    shouldBeChecked = currentMode == "WhisperSmall";
                                    Debug.WriteLine($"🎯 Whisper Small: mode='{currentMode}', checked={shouldBeChecked}");
                                }

                                subMenuItem.Checked = shouldBeChecked;
                            }
                        }
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Error updating context menu selection: {ex.Message}");
            }
        }

        /// <summary>
        /// Switch transcription mode and update configuration
        /// </summary>
        /// <param name="newMode">New transcription mode</param>
        private async Task SwitchTranscriptionModeAsync(string newMode)
        {
            try
            {
                Debug.WriteLine($"🔄 Switching transcription mode to: {newMode}");

                // Stop current service if recording
                if (_isRecording && _liveTranscriptionService != null)
                {
                    DebugT.WriteLine("🛑 Stopping current recording session before switching service");
                    await _liveTranscriptionService.StopListeningAsync();
                    _isRecording = false;
                }

                // Perform thorough cleanup for mode switching to free maximum memory
                if (_liveTranscriptionService != null)
                {
                    DebugT.WriteLine("🧹 Performing comprehensive cleanup for mode switching");

                    // For all service types, use standard stop and dispose
                    await _liveTranscriptionService.StopListeningAsync();

                    // Unsubscribe from events first
                    _liveTranscriptionService.TranscriptionReceived -= OnTranscriptionReceived;
                    _liveTranscriptionService.ErrorOccurred -= OnErrorOccurred;
                    _liveTranscriptionService.SessionStarted -= OnSessionStarted;
                    _liveTranscriptionService.SessionEnded -= OnSessionEnded;
                    _liveTranscriptionService.SpeechStarted -= OnSpeechStarted;
                    _liveTranscriptionService.SpeechStopped -= OnSpeechStopped;
                    _liveTranscriptionService.VoiceDetected -= OnVoiceDetectedImmediate;
                    _liveTranscriptionService.VoiceStopped -= OnVoiceStoppedImmediate;

                    _liveTranscriptionService.Dispose();
                    _liveTranscriptionService = null;

                    DebugT.WriteLine("✅ Complete cleanup and disposal finished for mode switching");
                }

                // Update configuration in memory
                if (_transcriptionServiceFactory != null)
                {
                    _transcriptionServiceFactory.UpdateMode(newMode);
                }

                // Update configuration file
                var configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
                var configJson = await File.ReadAllTextAsync(configPath);
                var configDocument = JsonDocument.Parse(configJson);

                // Update the transcription mode
                var rootElement = configDocument.RootElement;
                var updatedConfig = new Dictionary<string, object>();

                foreach (var property in rootElement.EnumerateObject())
                {
                    if (property.Name == "AzureSpeech")
                    {
                        var azureSpeechConfig = new Dictionary<string, object>();
                        foreach (var azureProperty in property.Value.EnumerateObject())
                        {
                            if (azureProperty.Name == "TranscriptionMode")
                            {
                                azureSpeechConfig[azureProperty.Name] = newMode;
                            }
                            else
                            {
                                azureSpeechConfig[azureProperty.Name] = azureProperty.Value.Clone();
                            }
                        }
                        updatedConfig[property.Name] = azureSpeechConfig;
                    }
                    else
                    {
                        updatedConfig[property.Name] = property.Value.Clone();
                    }
                }

                // Write updated configuration
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };
                var updatedJson = JsonSerializer.Serialize(updatedConfig, options);
                await File.WriteAllTextAsync(configPath, updatedJson);

                // Create new service with updated mode - now actually chooses the right service type!
                DebugT.WriteLine("🆕 Creating new transcription service with updated mode");
                if (_transcriptionServiceFactory != null)
                {
                    _liveTranscriptionService = _transcriptionServiceFactory.CreateLiveTranscriptionService();
                }

                // Re-subscribe to events
                if (_liveTranscriptionService != null)
                {
                    _liveTranscriptionService.TranscriptionReceived += OnTranscriptionReceived;
                    _liveTranscriptionService.ErrorOccurred += OnErrorOccurred;
                    _liveTranscriptionService.SessionStarted += OnSessionStarted;
                    _liveTranscriptionService.SessionEnded += OnSessionEnded;
                    _liveTranscriptionService.SpeechStarted += OnSpeechStarted;
                    _liveTranscriptionService.SpeechStopped += OnSpeechStopped;
                    _liveTranscriptionService.VoiceDetected += OnVoiceDetectedImmediate;
                    _liveTranscriptionService.VoiceStopped += OnVoiceStoppedImmediate;

                    // Pre-initialize the new service
                    DebugT.WriteLine("🔄 Pre-initializing new transcription service");
                    await _liveTranscriptionService.PreInitializeAsync();
                }

                // Update UI to reflect the new mode
                var modeDescription = newMode == "Whisper" ? "Whisper" : "Fast Transcription";
                UpdateStatus(STATUS_READY);

                // Update context menu checkmarks
                if (_contextMenu != null)
                {
                    UpdateContextMenuModeSelection(_contextMenu);
                }

                // Update UI visual indicators
                UpdateUIForMode(newMode);

                Debug.WriteLine($"✅ Successfully switched to {modeDescription} mode and recreated service");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to switch transcription mode: {ex.Message}");
            }
        }

        /// <summary>
        /// Public method for updating transcription mode from Settings window
        /// This method can be called by reflection from the Settings window
        /// </summary>
        /// <param name="newMode">The new transcription mode to switch to</param>
        public async Task UpdateTranscriptionMode(string newMode)
        {
            try
            {
                Debug.WriteLine($"📱 Settings window requested mode change to: {newMode}");
                await SwitchTranscriptionModeAsync(newMode);
                Debug.WriteLine($"✅ Mode change completed from Settings window: {newMode}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Error processing mode change from Settings window: {ex.Message}");
            }
        }

        /// <summary>
        /// Update UI visual indicators based on transcription mode
        /// </summary>
        /// <param name="mode">Current transcription mode</param>
        private void UpdateUIForMode(string mode)
        {
            try
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    // Update mode badge in header
                    var modeBadge = FindName("ModeBadge") as Border;
                    var modeBadgeText = FindName("ModeBadgeText") as TextBlock;
                    if (modeBadge != null && modeBadgeText != null)
                    {
                        switch (mode)
                        {
                            case "FastTranscription":
                                modeBadge.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(220, 252, 231)); // green-100
                                modeBadgeText.Text = "Fast";
                                modeBadgeText.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(22, 163, 74)); // green-600
                                break;
                            case "WhisperTiny":
                                modeBadge.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(219, 234, 254)); // blue-100
                                modeBadgeText.Text = "Whisper Tiny";
                                modeBadgeText.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(37, 99, 235)); // blue-600
                                break;
                            case "WhisperBase":
                                modeBadge.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(219, 234, 254)); // blue-100
                                modeBadgeText.Text = "Whisper Base";
                                modeBadgeText.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(37, 99, 235)); // blue-600
                                break;
                            case "WhisperSmall":
                                modeBadge.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(219, 234, 254)); // blue-100
                                modeBadgeText.Text = "Whisper Small";
                                modeBadgeText.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(37, 99, 235)); // blue-600
                                break;
                            case "Whisper": // Legacy support
                                modeBadge.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(219, 234, 254)); // blue-100
                                modeBadgeText.Text = "Whisper";
                                modeBadgeText.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(37, 99, 235)); // blue-600
                                break;
                            default:
                                modeBadge.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(229, 231, 235)); // gray-200
                                modeBadgeText.Text = "Mode";
                                modeBadgeText.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(17, 24, 39)); // gray-900
                                break;
                        }
                    }
                    // Update Instructions Panel text with mode information
                    var instructionText = FindName("InstructionsPanel") as Border;
                    if (instructionText?.Child is TextBlock textBlock)
                    {
                        switch (mode)
                        {
                            case "FastTranscription":
                                textBlock.Text = "Hold Right Alt + speak to dictate • Fast Mode (High Accuracy)";
                                break;
                            case "WhisperTiny":
                                textBlock.Text = "Hold Right Alt + speak to dictate • Whisper Tiny Mode (Local)";
                                break;
                            case "WhisperBase":
                                textBlock.Text = "Hold Right Alt + speak to dictate • Whisper Base Mode (Local)";
                                break;
                            case "WhisperSmall":
                                textBlock.Text = "Hold Right Alt + speak to dictate • Whisper Small Mode (Local)";
                                break;
                            case "Whisper": // Legacy support
                                textBlock.Text = "Hold Right Alt + speak to dictate • Whisper Mode (Local)";
                                break;
                            default:
                                textBlock.Text = "Hold Right Alt + speak to dictate";
                                break;
                        }
                    }

                    // Update microphone circle color based on mode
                    var microphoneCircle = FindName("MicrophoneCircle") as Ellipse;
                    if (microphoneCircle != null)
                    {
                        switch (mode)
                        {
                            case "FastTranscription":
                                microphoneCircle.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(34, 197, 94)); // Green for Fast
                                microphoneCircle.Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(22, 163, 74));
                                break;
                            case "WhisperTiny":
                            case "WhisperBase":
                            case "WhisperSmall":
                                microphoneCircle.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(59, 130, 246)); // Blue for Whisper
                                microphoneCircle.Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(37, 99, 235));
                                break;
                            case "Whisper": // Legacy support
                                microphoneCircle.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(59, 130, 246)); // Blue for Whisper
                                microphoneCircle.Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(37, 99, 235));
                                break;
                            default:
                                microphoneCircle.Fill = new SolidColorBrush(Colors.White);
                                microphoneCircle.Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(226, 232, 240));
                                break;
                        }
                    }

                    // Update microphone icon color
                    var microphoneIcon = FindName("MicrophoneIcon") as System.Windows.Shapes.Path;
                    if (microphoneIcon != null)
                    {
                        switch (mode)
                        {
                            case "FastTranscription":
                                microphoneIcon.Fill = new SolidColorBrush(Colors.White);
                                break;
                            case "WhisperTiny":
                            case "WhisperBase":
                            case "WhisperSmall":
                                microphoneIcon.Fill = new SolidColorBrush(Colors.White);
                                break;
                            case "Whisper": // Legacy support
                                microphoneIcon.Fill = new SolidColorBrush(Colors.White);
                                break;
                            default:
                                microphoneIcon.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(99, 102, 241));
                                break;
                        }
                    }
                });

                DebugT.WriteLine("✅ UI updated for {mode} mode");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Error updating UI for mode {mode}: {ex.Message}");
            }
        }

        private void InitializeAnimations()
        {
            try
            {
                // Get animation storyboards from resources
                _microphonePulse = FindResource("MicrophonePulse") as Storyboard;
                _listeningWave = FindResource("ListeningWave") as Storyboard;
                _speakingAnimation = FindResource("SpeakingAnimation") as Storyboard;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Animation initialization warning: {ex.Message}");
            }
        }

        private void InitializeSystemTray()
        {
            try
            {
                // Create system tray icon
                _notifyIcon = new WinForms.NotifyIcon();

                // Set the icon - load directly from embedded resources
                TryLoadIconFromResources();

                _notifyIcon.Text = "PowerVoice - Professional Dictation";
                _notifyIcon.Visible = true;

                // Create context menu
                _contextMenu = new WinForms.ContextMenuStrip();

                // Show/Hide menu item
                var showHideItem = new WinForms.ToolStripMenuItem("Show PowerVoice");
                showHideItem.Click += (s, e) => ShowWindow();
                _contextMenu.Items.Add(showHideItem);

                _contextMenu.Items.Add(new WinForms.ToolStripSeparator());

                // Transcription Mode submenu
                var modeMenuItem = new WinForms.ToolStripMenuItem("Transcription Mode");

                var fastModeItem = new WinForms.ToolStripMenuItem("Fast")
                {
                    CheckOnClick = false,
                    Checked = GetTranscriptionMode() == "FastTranscription"
                };
                fastModeItem.Click += async (s, e) => await SwitchTranscriptionModeAsync("FastTranscription");

                var whisperModeItem = new WinForms.ToolStripMenuItem("Whisper (Legacy)")
                {
                    CheckOnClick = false,
                    Checked = GetTranscriptionMode() == "Whisper"
                };
                whisperModeItem.Click += async (s, e) => await SwitchTranscriptionModeAsync("Whisper");

                var whisperTinyModeItem = new WinForms.ToolStripMenuItem("Whisper Tiny")
                {
                    CheckOnClick = false,
                    Checked = GetTranscriptionMode() == "WhisperTiny"
                };
                whisperTinyModeItem.Click += async (s, e) => await SwitchTranscriptionModeAsync("WhisperTiny");

                var whisperBaseModeItem = new WinForms.ToolStripMenuItem("Whisper Base")
                {
                    CheckOnClick = false,
                    Checked = GetTranscriptionMode() == "WhisperBase"
                };
                whisperBaseModeItem.Click += async (s, e) => await SwitchTranscriptionModeAsync("WhisperBase");

                var whisperSmallModeItem = new WinForms.ToolStripMenuItem("Whisper Small")
                {
                    CheckOnClick = false,
                    Checked = GetTranscriptionMode() == "WhisperSmall"
                };
                whisperSmallModeItem.Click += async (s, e) => await SwitchTranscriptionModeAsync("WhisperSmall");

                modeMenuItem.DropDownItems.Add(fastModeItem);
                modeMenuItem.DropDownItems.Add(whisperTinyModeItem);
                modeMenuItem.DropDownItems.Add(whisperBaseModeItem);
                modeMenuItem.DropDownItems.Add(whisperSmallModeItem);
                modeMenuItem.DropDownItems.Add(whisperModeItem);
                _contextMenu.Items.Add(modeMenuItem);

                _contextMenu.Items.Add(new WinForms.ToolStripSeparator());

                // Settings menu item
                var settingsItem = new WinForms.ToolStripMenuItem("Settings");
                settingsItem.Click += (s, e) =>
                {
                    try
                    {
                        var settingsWindow = new SettingsWindow
                        {
                            Owner = this,
                            WindowStartupLocation = WindowStartupLocation.CenterOwner
                        };
                        settingsWindow.ShowDialog();

                        // Refresh the context menu to reflect any changes
                        UpdateContextMenuModeSelection(_contextMenu);
                        UpdateStatus(STATUS_READY); // Refresh status display
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"⚠️ Error opening settings: {ex.Message}");
                        System.Windows.MessageBox.Show($"Failed to open settings: {ex.Message}", "Settings Error",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                };
                _contextMenu.Items.Add(settingsItem);

                _contextMenu.Items.Add(new WinForms.ToolStripSeparator());

                // Exit menu item
                var exitItem = new WinForms.ToolStripMenuItem("Exit");
                exitItem.Click += (s, e) => ExitApplication();
                _contextMenu.Items.Add(exitItem);

                _notifyIcon.ContextMenuStrip = _contextMenu;

                // Double-click to show window
                _notifyIcon.DoubleClick += (s, e) => ShowWindow();

                DebugT.WriteLine("✅ System tray initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ System tray initialization warning: {ex.Message}");
            }
        }

        private void TryLoadIconFromResources()
        {
            try
            {
                // Use embedded ICO file instead of System.Drawing for memory efficiency
                var iconUri = new Uri("pack://application:,,,/powervoice-modern.ico");
                var iconStream = System.Windows.Application.GetResourceStream(iconUri);

                if (iconStream != null)
                {
                    var icon = new System.Drawing.Icon(iconStream.Stream);
                    _notifyIcon.Icon = icon;
                    DebugT.WriteLine("✅ Loaded PowerVoice icon from embedded resources");
                }
                else
                {
                    DebugT.WriteLine("⚠️ PowerVoice icon not found in resources, using fallback");
                    TryCreateSimpleTextIcon();
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Icon loading warning: {ex.Message}");
                // Fallback to simple icon
                TryCreateSimpleTextIcon();
            }
        }

        private void TryCreateSimpleTextIcon()
        {
            try
            {
                // Use Windows built-in system icons instead of System.Drawing
                _notifyIcon.Icon = System.Drawing.SystemIcons.Application;
                DebugT.WriteLine("✅ Using Windows system application icon");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Failed to load system icon: {ex.Message}");
                // Final fallback - null icon (Windows will provide default)
                _notifyIcon.Icon = null;
                DebugT.WriteLine("⚠️ Using default system tray icon");
            }
        }

        private void ShowWindow()
        {
            Show();
            WindowState = WindowState.Normal;

            // Bring window to front using proper methods without disabling always-on-top
            if (!IsActive)
            {
                // Multiple approaches to ensure window comes to front properly
                Activate();
                Focus();

                // For stubborn cases, use a brief topmost toggle that preserves the intended behavior
                var wasTopmost = Topmost;
                if (!wasTopmost)
                {
                    Topmost = true;
                    Topmost = false;
                }
            }

            // Ensure always-on-top behavior is correct for current state
            UpdateAlwaysOnTopBehavior();
        }

        private void ExitApplication()
        {
            _isExiting = true;
            _notifyIcon?.Dispose();
            Close();
        }

        private void UpdateStatus(string text)
        {
            // Skip UI updates when window is minimized to optimize performance for 24/7 background operation
            if (!ShouldUpdateUI)
            {
                Debug.WriteLine($"📱 Skipping UI update (minimized): '{text}'");
                return;
            }

            try
            {
                // Show/hide instructions based on status
                if (InstructionsPanel != null)
                {
                    bool isReady = text == STATUS_READY;
                    InstructionsPanel.Visibility = isReady ? Visibility.Visible : Visibility.Collapsed;
                }

                // Update tray icon text with transcription mode info
                if (_notifyIcon != null)
                {
                    var mode = GetTranscriptionMode();
                    var modeDescription = mode switch
                    {
                        "WhisperTiny" => "Whisper Tiny",
                        "WhisperBase" => "Whisper Base",
                        "WhisperSmall" => "Whisper Small",
                        "Whisper" => "Whisper",
                        _ => "Fast Transcription"
                    };

                    // For ready status, show clean format without redundancy
                    if (text == STATUS_READY)
                    {
                        _notifyIcon.Text = $"Ready | Mode: {modeDescription}";
                    }
                    else
                    {
                        // For other statuses, just show the status without app name
                        _notifyIcon.Text = text;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Status update warning: {ex.Message}");
            }
        }

        private void StartListeningAnimation()
        {
            // Skip animation when window is minimized to optimize performance for 24/7 background operation
            if (!ShouldUpdateUI)
            {
                DebugT.WriteLine("📱 Skipping start animation (minimized)");
                return;
            }

            try
            {
                // Change microphone to active state with modern colors
                if (MicrophoneCircle != null)
                {
                    MicrophoneCircle.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(99, 102, 241)); // Indigo-500
                    MicrophoneCircle.Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(79, 70, 229)); // Indigo-600
                }

                if (MicrophoneIcon != null)
                {
                    MicrophoneIcon.Fill = new SolidColorBrush(Colors.White);
                }

                // Show and animate outer glow
                if (OuterGlow != null)
                {
                    OuterGlow.Visibility = Visibility.Visible;
                    OuterGlow.Opacity = 0.6;
                }

                // Show wave rings
                if (WaveRing1 != null)
                {
                    WaveRing1.Visibility = Visibility.Visible;
                    WaveRing1.Opacity = 0.6;
                }

                if (WaveRing2 != null)
                {
                    WaveRing2.Visibility = Visibility.Visible;
                    WaveRing2.Opacity = 0.4;
                }

                // Start animations
                _microphonePulse?.Begin();
                _listeningWave?.Begin();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Animation start warning: {ex.Message}");
            }
        }

        private void StopListeningAnimation()
        {
            // Skip animation when window is minimized to optimize performance for 24/7 background operation
            if (!ShouldUpdateUI)
            {
                DebugT.WriteLine("📱 Skipping stop animation (minimized)");
                return;
            }

            try
            {
                var currentMode = GetTranscriptionMode();

                // Restore microphone circle and icon to initial mode-specific state
                var microphoneCircle = FindName("MicrophoneCircle") as Ellipse;
                var microphoneIcon = FindName("MicrophoneIcon") as System.Windows.Shapes.Path;

                if (microphoneCircle != null && microphoneIcon != null)
                {
                    if (currentMode == "FastTranscription")
                    {
                        // Fast mode: Green background with white icon (match UpdateUIForMode)
                        microphoneCircle.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(34, 197, 94)); // Green for Fast
                        microphoneCircle.Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(22, 163, 74));
                        microphoneIcon.Fill = new SolidColorBrush(Colors.White); // White icon
                    }
                    else if (currentMode == "Whisper")
                    {
                        // Whisper mode: Blue background with white icon (match UpdateUIForMode)
                        microphoneCircle.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(59, 130, 246)); // Blue for Whisper
                        microphoneCircle.Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(37, 99, 235));
                        microphoneIcon.Fill = new SolidColorBrush(Colors.White); // White icon
                    }
                    else
                    {
                        // Default: White background with gray border
                        microphoneCircle.Fill = new SolidColorBrush(Colors.White);
                        microphoneCircle.Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(226, 232, 240));
                        microphoneIcon.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(99, 102, 241)); // Default icon color
                    }
                }

                // Hide beautiful speaking glow
                var speakingGlow = FindName("SpeakingGlow") as UIElement;
                if (speakingGlow != null)
                {
                    speakingGlow.Visibility = Visibility.Collapsed;
                }

                // Hide speaking wave elements
                var speakingWave1 = FindName("SpeakingWave1") as UIElement;
                if (speakingWave1 != null)
                {
                    speakingWave1.Visibility = Visibility.Collapsed;
                }
                var speakingWave2 = FindName("SpeakingWave2") as UIElement;
                if (speakingWave2 != null)
                {
                    speakingWave2.Visibility = Visibility.Collapsed;
                }
                var speakingWave3 = FindName("SpeakingWave3") as UIElement;
                if (speakingWave3 != null)
                {
                    speakingWave3.Visibility = Visibility.Collapsed;
                }

                // Hide outer glow
                var outerGlow = FindName("OuterGlow") as UIElement;
                if (outerGlow != null)
                {
                    outerGlow.Visibility = Visibility.Collapsed;
                    if (outerGlow is FrameworkElement fe) fe.Opacity = 0;
                }

                // Hide wave rings
                var waveRing1 = FindName("WaveRing1") as UIElement;
                if (waveRing1 != null)
                {
                    waveRing1.Visibility = Visibility.Collapsed;
                }

                var waveRing2 = FindName("WaveRing2") as UIElement;
                if (waveRing2 != null)
                {
                    waveRing2.Visibility = Visibility.Collapsed;
                }

                // Stop all animations
                _microphonePulse?.Stop();
                _listeningWave?.Stop();
                _speakingAnimation?.Stop();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Animation stop warning: {ex.Message}");
            }
        }

        private void ShowSpeakingIndicator()
        {
            if (!ShouldUpdateUI)
            {
                DebugT.WriteLine("📱 Skipping speaking indicator (minimized)");
                return;
            }

            try
            {
                // Microphone icon stays visible - just enhance it with beautiful effects

                // Show beautiful speaking glow
                var speakingGlow = FindName("SpeakingGlow") as UIElement;
                if (speakingGlow != null)
                {
                    speakingGlow.Visibility = Visibility.Visible;
                }

                // Show speaking wave elements
                var speakingWave1 = FindName("SpeakingWave1") as UIElement;
                if (speakingWave1 != null)
                {
                    speakingWave1.Visibility = Visibility.Visible;
                }
                var speakingWave2 = FindName("SpeakingWave2") as UIElement;
                if (speakingWave2 != null)
                {
                    speakingWave2.Visibility = Visibility.Visible;
                }
                var speakingWave3 = FindName("SpeakingWave3") as UIElement;
                if (speakingWave3 != null)
                {
                    speakingWave3.Visibility = Visibility.Visible;
                }

                // Start beautiful speaking animation
                _speakingAnimation?.Begin();

                DebugT.WriteLine("✨ Started beautiful speaking wave animation with microphone visible");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Speaking indicator warning: {ex.Message}");
            }
        }

        private void StopSpeakingIndicator()
        {
            if (!ShouldUpdateUI)
            {
                DebugT.WriteLine("📱 Skipping stop speaking indicator (minimized)");
                return;
            }

            try
            {
                // Hide beautiful speaking glow
                var speakingGlow = FindName("SpeakingGlow") as UIElement;
                if (speakingGlow != null)
                {
                    speakingGlow.Visibility = Visibility.Collapsed;
                }

                // Hide speaking wave elements
                var speakingWave1 = FindName("SpeakingWave1") as UIElement;
                if (speakingWave1 != null)
                {
                    speakingWave1.Visibility = Visibility.Collapsed;
                }
                var speakingWave2 = FindName("SpeakingWave2") as UIElement;
                if (speakingWave2 != null)
                {
                    speakingWave2.Visibility = Visibility.Collapsed;
                }
                var speakingWave3 = FindName("SpeakingWave3") as UIElement;
                if (speakingWave3 != null)
                {
                    speakingWave3.Visibility = Visibility.Collapsed;
                }

                // Stop speaking animation but keep listening animation running
                _speakingAnimation?.Stop();

                DebugT.WriteLine("🔇 Stopped speaking wave animation - microphone remains in listening state");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Stop speaking indicator warning: {ex.Message}");
            }
        }

        private void InitializeGlobalHotkey()
        {
            try
            {
                DebugT.WriteLine("🔧 Initializing Right Alt hotkey detection...");

                // Subscribe to key press events
                GlobalKeyboardHook.KeyPressed += OnKeyPressed;

                // Start the global keyboard hook
                GlobalKeyboardHook.StartHook();

                UpdateStatus(STATUS_READY);
                DebugT.WriteLine("✅ Right Alt hotkey detection initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to initialize Right Alt hotkey: {ex.Message}");
                UpdateStatus("⚠️ Right Alt hotkey disabled");
            }
        }

        private void OnKeyPressed(object? sender, GlobalKeyboardHook.KeyPressedEventArgs e)
        {
            try
            {
                // Only handle Right Alt key (VK: A5)
                if (e.VirtualKeyCode == 0xA5) // Right Alt key
                {
                    if (e.EventType == "SysKeyDown" && !_isRightAltPressed)
                    {
                        // Right Alt pressed - start voice recognition IMMEDIATELY
                        _isRightAltPressed = true;

                        // Clear any previous text segments at start of new session
                        lock (_textQueueLock)
                        {
                            _textSegmentQueue.Clear();
                            DebugT.WriteLine("🗑️ Cleared text segment queue for new Alt session");
                        }

                        Debug.WriteLine("🔥 RIGHT ALT PRESSED - STARTING VOICE RECOGNITION!");
                        DebugT.WriteLine("🎤 Right Alt pressed - starting voice recognition IMMEDIATELY");

                        // CRITICAL: Start recording immediately without any UI delays
                        // Fire and forget the async operation to avoid blocking
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                Debug.WriteLine("🔥 Task.Run executing StartRecordingImmediate...");
                                await StartRecordingImmediate();
                                Debug.WriteLine("🔥 Task.Run completed StartRecordingImmediate!");
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"❌ CRITICAL: Task.Run exception: {ex.Message}");
                                Debug.WriteLine($"❌ Immediate recording start failed: {ex.Message}");
                            }
                        });

                        // Update UI asynchronously after starting recording to avoid delays
                        Dispatcher.BeginInvoke(() => StartListeningUI());
                    }
                    else if (e.EventType == "KeyUp" && _isRightAltPressed)
                    {
                        // Right Alt released - add grace period to capture final words
                        _isRightAltPressed = false;

                        // CRITICAL: Clear Alt key state immediately to prevent external apps from processing Alt release
                        // This must happen before the grace period to prevent Notepad and other apps from showing menus
                        DebugT.WriteLine("🔧 Alt key released - clearing Alt state immediately to prevent menu activation");
                        UniversalDictation.ClearAltKeyImmediately();
                        int gracePeriodMs = 300; // Default for fast modes

                        DebugT.WriteLine($"⏸️ Right Alt released - adding {gracePeriodMs}ms grace period for final words");

                        Dispatcher.Invoke(async () =>
                        {
                            // CRITICAL: 300ms grace period to capture final words people speak while releasing key
                            // This addresses the psychological issue where people start releasing the key
                            // before they finish their last word/syllable due to:
                            // 1. Key travel time (2-5ms)
                            // 2. Human anticipation (100-200ms early release)
                            // 3. Speech endings getting cut off

                            // Standard grace period for all modes
                            int gracePeriodMs = 300; // Default 300ms grace period

                            Debug.WriteLine($"⏰ Grace period: {gracePeriodMs}ms");
                            DebugT.WriteLine($"⏸️ Right Alt released - adding {gracePeriodMs}ms grace period for final words");
                            await Task.Delay(gracePeriodMs);

                            await StopRecording();
                            StopListeningUI();

                            // Process all accumulated text segments
                            string combinedText = string.Empty;
                            lock (_textQueueLock)
                            {
                                if (_textSegmentQueue.Count > 0)
                                {
                                    combinedText = string.Join(" ", _textSegmentQueue).Trim();
                                    Debug.WriteLine($"🔗 Combined {_textSegmentQueue.Count} text segments: '{combinedText}'");
                                    _textSegmentQueue.Clear();
                                }
                            }

                            // Send combined text now that Alt is fully released
                            if (!string.IsNullOrEmpty(combinedText))
                            {
                                Debug.WriteLine($"🚀 Alt released, sending combined text: '{combinedText}'");

                                // Send text in background now that Alt is released
                                _ = Task.Run(() =>
                                {
                                    try
                                    {
                                        UniversalDictation.SendTextToActiveWindow(combinedText);
                                    }
                                    catch (Exception ex)
                                    {
                                        Debug.WriteLine($"❌ Combined text injection error: {ex.Message}");
                                    }
                                });
                            }
                            else
                            {
                                DebugT.WriteLine("ℹ️ No text segments to send");
                            }
                        });
                        DebugT.WriteLine($"✅ Right Alt released - {gracePeriodMs} grace period completed, voice recognition stopped");

                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Error handling key press: {ex.Message}");
            }
        }

        // System tray minimize functionality
        public void MinimizeToTray_Click(object sender, RoutedEventArgs e)
        {
            Hide(); // Hide to system tray
            // Notification removed - app minimizes silently to tray
        }

        // Settings button functionality
        public void Settings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DebugT.WriteLine("⚙️ Opening settings window...");

                var settingsWindow = new SettingsWindow
                {
                    Owner = this,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner
                };

                // Show settings window as modal dialog
                bool? result = settingsWindow.ShowDialog();

                if (result == true)
                {
                    DebugT.WriteLine("✅ Settings saved and applied");
                }
                else
                {
                    DebugT.WriteLine("❌ Settings cancelled");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Error opening settings: {ex.Message}");
                System.Windows.MessageBox.Show($"Failed to open settings: {ex.Message}", "Settings Error",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Add drag functionality for moving the window
        public void Window_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                DragMove();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Drag move warning: {ex.Message}");
            }
        }

        private async Task StartRecordingImmediate()
        {
            if (_isRecording) return; // Prevent double start

            try
            {
                DebugT.WriteLine("🔥 URGENT DEBUG: StartRecordingImmediate() called!");
                DebugT.WriteLine($"🔥 _liveTranscriptionService is null: {_liveTranscriptionService == null}");

                _isRecording = true;
                DebugT.WriteLine("🚀 IMMEDIATE: Starting Speech recognition with zero UI delay...");

                if (_liveTranscriptionService != null)
                {
                    DebugT.WriteLine("🔥 About to call StartListeningAsync()...");
                    // Start listening immediately - this is the CRITICAL path for capturing first words
                    await _liveTranscriptionService.StartListeningAsync();
                    DebugT.WriteLine("✅ IMMEDIATE: Speech recognition started - ready to capture speech");
                }
                else
                {
                    DebugT.WriteLine("❌ CRITICAL: _liveTranscriptionService is NULL!");
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ CRITICAL ERROR in StartRecordingImmediate: {ex.Message}");
                DebugT.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                Debug.WriteLine($"❌ IMMEDIATE: Failed to start recording: {ex.Message}");
                _isRecording = false;

                // Only update UI on error, and do it asynchronously to not block speech service
                _ = Dispatcher.BeginInvoke(() =>
                {
                    UpdateStatus($"❌ Recording failed: {ex.Message}");
                });
            }
        }

        private async Task StartRecording()
        {
            if (_isRecording) return; // Prevent double start

            try
            {
                _isRecording = true;
                DebugT.WriteLine("🎤 Starting Speech recognition...");

                if (_liveTranscriptionService != null)
                {
                    await _liveTranscriptionService.StartListeningAsync();
                    DebugT.WriteLine("✅ Speech recognition started successfully");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to start recording: {ex.Message}");
                _isRecording = false;

                // Reset UI state on error
                Dispatcher.Invoke(() =>
                {
                    UpdateStatus($"❌ Recording failed: {ex.Message}");
                });
            }
        }

        private async Task StopRecording()
        {
            if (!_isRecording) return; // Prevent double stop



            try
            {
                _isRecording = false;
                DebugT.WriteLine("⏹️ Stopping Speech recognition...");

                if (_liveTranscriptionService != null)
                {
                    await _liveTranscriptionService.StopListeningAsync();
                    DebugT.WriteLine("✅ Speech recognition stopped successfully");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to stop recording: {ex.Message}");
                UpdateStatus($"❌ Stop failed: {ex.Message}");
            }
        }

        // Live Transcription Service Event Handlers
        private void OnTranscriptionReceived(object? sender, TranscriptionEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                try
                {
                    var latencyMs = e.Latency?.TotalMilliseconds ?? 0;
                    var isInterim = e.Text.StartsWith("[INTERIM]", StringComparison.Ordinal);
                    var actualText = isInterim ? e.Text.Substring(9).Trim() : e.Text; // Remove [INTERIM] prefix

                    Debug.WriteLine($"📝 Transcription: {actualText} (Interim: {isInterim}, Latency: {latencyMs:F0}ms)");

                    if (isInterim)
                    {
                        // Interim results - no UI changes needed since immediate voice detection handles speaking animation
                        Debug.WriteLine($"📝 Interim result received: '{actualText}' - speaking animation already active from voice detection");
                    }
                    else
                    {
                        // Final result received - go back to listening state
                        UpdateStatus(STATUS_LISTENING);

                        // Queue this text segment for processing when Alt is released
                        // This ensures we capture ALL segments, even with pauses in speech
                        if (!string.IsNullOrEmpty(actualText))
                        {
                            lock (_textQueueLock)
                            {
                                _textSegmentQueue.Enqueue(actualText);
                                Debug.WriteLine($"📋 Queued text segment #{_textSegmentQueue.Count}: '{actualText}'");

                                // Show cumulative text in debug for monitoring
                                var allSegments = string.Join(" ", _textSegmentQueue);
                                Debug.WriteLine($"📎 All segments so far: '{allSegments}'");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ Error in transcription handler: {ex.Message}");
                }
            });
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            // If not explicitly exiting, just hide the window
            if (!_isExiting)
            {
                e.Cancel = true;
                Hide();
                // Notification removed - app hides silently to tray
                return;
            }

            try
            {
                // Stop global keyboard hook
                GlobalKeyboardHook.StopHook();

                // Cleanup resources
                _liveTranscriptionService?.Dispose();

                // Dispose system tray
                _notifyIcon?.Dispose();

                DebugT.WriteLine("✅ Application cleanup completed");

                // Dispose resources
                Dispose();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Cleanup warning: {ex.Message}");
            }

            base.OnClosing(e);
        }

        // Simplified event handlers for new UI
        private void OnErrorOccurred(object? sender, string errorMessage)
        {
            Dispatcher.Invoke(() =>
            {
                Debug.WriteLine($"❌ Live Speech Error: {errorMessage}");

                // Only update UI and show dialog if window is visible
                if (ShouldUpdateUI)
                {
                    UpdateStatus($"❌ Error: {errorMessage}");
                    System.Windows.MessageBox.Show($"Speech recognition error: {errorMessage}", "Speech Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                else
                {
                    DebugT.WriteLine("📱 Skipping error UI updates (minimized)");
                }
            });
        }

        private void OnSessionStarted(object? sender, EventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                DebugT.WriteLine("🟢 Speech session started");

                // Only update UI if window is visible
                if (ShouldUpdateUI)
                {
                    UpdateStatus(STATUS_LISTENING);
                }
                else
                {
                    DebugT.WriteLine("📱 Skipping session start UI update (minimized)");
                }
            });
        }

        private void OnSessionEnded(object? sender, EventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                DebugT.WriteLine("🔴 Speech session ended");

                // Only update UI if window is visible
                if (ShouldUpdateUI)
                {
                    UpdateStatus(STATUS_READY);
                }
                else
                {
                    DebugT.WriteLine("📱 Skipping session end UI update (minimized)");
                }
            });
        }

        private void OnSpeechStarted(object? sender, EventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                DebugT.WriteLine("🎤 Speech detected - processing...");

                // Only update UI if window is visible
                if (ShouldUpdateUI)
                {
                    UpdateStatus("🎤 Speech detected...");
                }
                else
                {
                    DebugT.WriteLine("📱 Skipping speech start UI update (minimized)");
                }
            });
        }

        private void OnSpeechStopped(object? sender, EventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                DebugT.WriteLine("🔇 Speech ended - finalizing...");
                // No UI update needed for speech stopped
            });
        }

        /// <summary>
        /// Handles immediate voice detection for instant speaking animation (triggers before Azure processes audio)
        /// </summary>
        private void OnVoiceDetectedImmediate(object? sender, EventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                DebugT.WriteLine("🎤 INSTANT: Voice detected by audio level analysis - starting speaking animation immediately");

                // Only update UI if window is visible
                if (ShouldUpdateUI)
                {
                    UpdateStatus(STATUS_SPEAKING);
                    ShowSpeakingIndicator();
                }
                else
                {
                    DebugT.WriteLine("📱 Skipping instant voice detection UI update (minimized)");
                }
            });
        }

        /// <summary>
        /// Handles immediate voice stop detection for instant animation stop (triggers before Azure processes audio)
        /// </summary>
        private void OnVoiceStoppedImmediate(object? sender, EventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                DebugT.WriteLine("🔇 INSTANT: Voice stopped by audio level analysis - stopping speaking animation and returning to listening state");

                // Only update UI if window is visible
                if (ShouldUpdateUI)
                {
                    UpdateStatus(STATUS_LISTENING);

                    // Stop the speaking animation immediately when voice stops
                    StopSpeakingIndicator();
                }
                else
                {
                    DebugT.WriteLine("📱 Skipping instant voice stop UI update (minimized)");
                }
            });
        }

        public void Dispose()
        {
            try
            {
                // Stop global keyboard hook
                GlobalKeyboardHook.StopHook();

                // Shutdown text injection thread
                UniversalDictation.Shutdown();

                // Dispose Live Transcription Service
                _liveTranscriptionService?.Dispose();

                // Dispose system tray icon
                _notifyIcon?.Dispose();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Disposal warning: {ex.Message}");
            }
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Initialize always-on-top behavior that respects minimized state
        /// </summary>
        private void InitializeAlwaysOnTopBehavior()
        {
            // Subscribe to state changes to manage always-on-top behavior
            StateChanged += MainWindow_StateChanged;

            // Ensure window starts as always-on-top if not minimized
            UpdateAlwaysOnTopBehavior();
        }

        /// <summary>
        /// Handle window state changes to manage always-on-top behavior
        /// </summary>
        private void MainWindow_StateChanged(object? sender, EventArgs e)
        {
            UpdateAlwaysOnTopBehavior();
        }

        /// <summary>
        /// Update always-on-top behavior based on current window state
        /// </summary>
        private void UpdateAlwaysOnTopBehavior()
        {
            try
            {
                // Always-on-top should be enabled when window is NOT minimized
                bool shouldBeTopmost = WindowState != WindowState.Minimized;

                if (Topmost != shouldBeTopmost)
                {
                    Topmost = shouldBeTopmost;
                    Debug.WriteLine($"🔝 Always-on-top behavior updated: {(shouldBeTopmost ? "ENABLED" : "DISABLED")} (State: {WindowState})");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Error updating always-on-top behavior: {ex.Message}");
            }
        }
    }
}
