Whisper Models Directory
========================

This directory contains Whisper model files for local transcription.

Due to file size limitations, model files are not included in the repository.

To use Whisper mode, download the required model files:

1. Small English Model (recommended):
   - Download: ggml-small.en.bin
   - From: https://huggingface.co/ggerganov/whisper.cpp/tree/main
   - Size: ~465 MB
   - Place in this directory

2. Other supported models:
   - ggml-base.en.bin (~140 MB)
   - ggml-medium.en.bin (~760 MB) 
   - ggml-large-v2.bin (~1.5 GB)

You can change the model in appsettings.json at Whisper:AudioTranscription:Model, 
but ensure the file is present in this folder and follows the name pattern: ggml-<model>.bin

The application will automatically detect available models in this directory.
