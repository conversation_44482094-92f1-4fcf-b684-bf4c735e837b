<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows10.0.26100.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <!-- Debug configuration - ENABLED for debugging -->
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <!-- Console window configuration -->
    <CreateNoWindow>true</CreateNoWindow>
    <WindowsAppSDKSelfContained>false</WindowsAppSDKSelfContained>
    <DisableWin32HostfxrMuxer>true</DisableWin32HostfxrMuxer>
    <EnableNETAnalyzers>false</EnableNETAnalyzers>
    <UseApplicationTrust>false</UseApplicationTrust>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
    <!-- Platform configuration -->
    <PlatformTarget>x64</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <RuntimeIdentifiers>win-x64;win-x86</RuntimeIdentifiers>
    <!-- Application Icon -->
    <ApplicationIcon>powervoice-modern.ico</ApplicationIcon>
    <!-- Dependency management -->
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <PublishSingleFile>false</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
    <DisableImplicitNuGetFallbackFolder>false</DisableImplicitNuGetFallbackFolder>
    <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <CopyBuildOutputToOutputDirectory>true</CopyBuildOutputToOutputDirectory>
    <CopyOutputSymbolsToOutputDirectory>true</CopyOutputSymbolsToOutputDirectory>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <CopyLocalRuntimeTargetAssets>true</CopyLocalRuntimeTargetAssets>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <_ResolveReferenceDependencies>true</_ResolveReferenceDependencies>
    <CopyConflictingTransitiveContent>true</CopyConflictingTransitiveContent>
    <UseAppHost>true</UseAppHost>
  </PropertyGroup>

  <!-- Debug-specific configuration -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <CheckForOverflowUnderflow>true</CheckForOverflowUnderflow>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <!-- Additional debug features -->
    <IncludeSourceRevisionInInformationalVersion>true</IncludeSourceRevisionInInformationalVersion>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
  </PropertyGroup>

  <!-- Release-specific configuration -->
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DebugSymbols>false</DebugSymbols>
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <DefineConstants>TRACE</DefineConstants>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <!-- Production-specific configuration - ULTRA LEAN BUILD -->
  <PropertyGroup Condition="'$(Configuration)' == 'Production'">
    <DebugSymbols>false</DebugSymbols>
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <DefineConstants>TRACE;PRODUCTION</DefineConstants>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <CreateNoWindow>true</CreateNoWindow>
    <EnableNETAnalyzers>false</EnableNETAnalyzers>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>

    <!-- SELECTIVE TRIMMING - WPF/WinForms Compatible - DISABLED for compatibility -->
    <PublishTrimmed>false</PublishTrimmed>
    <TrimMode>partial</TrimMode>
    <TrimmerSingleWarn>false</TrimmerSingleWarn>
    <EnableTrimAnalyzer>false</EnableTrimAnalyzer>
    <IsTrimmable>false</IsTrimmable>
    <TrimUnusedDependencies>false</TrimUnusedDependencies>
    <TrimmerRemoveSymbols>true</TrimmerRemoveSymbols>

    <!-- MINIMAL FEATURE SET -->
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>true</UseWPF>
    <IncludeNativeLibrariesForSelfExtract>false</IncludeNativeLibrariesForSelfExtract>

    <!-- SELF-CONTAINED ULTRA-LEAN BUILD -->
    <SelfContained>true</SelfContained>
    <PublishSingleFile>true</PublishSingleFile>
    <PublishReadyToRun>false</PublishReadyToRun>
    <PublishReadyToRunShowWarnings>false</PublishReadyToRunShowWarnings>

    <!-- MODERATE FEATURE ELIMINATION for sub-20MB with compatibility -->
    <InvariantGlobalization>false</InvariantGlobalization>
    <EnableUnsafeUTF7Encoding>false</EnableUnsafeUTF7Encoding>
    <EnableUnsafeBinaryFormatterSerialization>false</EnableUnsafeBinaryFormatterSerialization>
    <EventSourceSupport>true</EventSourceSupport>
    <UseSystemResourceKeys>false</UseSystemResourceKeys>
    <StackTraceSupport>true</StackTraceSupport>
    <StartupHookSupport>false</StartupHookSupport>
    <EnableComHosting>false</EnableComHosting>
    <EnableGeneratedComInterfaceComImportInterop>false</EnableGeneratedComInterfaceComImportInterop>

    <!-- ELIMINATE UNUSED RUNTIME FEATURES -->
    <DebuggerSupport>true</DebuggerSupport>
    <HttpActivityPropagationSupport>true</HttpActivityPropagationSupport>
    <MetadataUpdaterSupport>false</MetadataUpdaterSupport>
    <CustomResourceTypesSupport>true</CustomResourceTypesSupport>
    <SerializationBinder>false</SerializationBinder>
  </PropertyGroup>

  <ItemGroup>
    <!-- ULTRA-LEAN: NAudio + HTTP-only cloud services for sub-10MB target -->
    <PackageReference Include="NAudio.Core" Version="2.2.1" />
    <PackageReference Include="NAudio.WinMM" Version="2.2.1" />
    <!-- MINIMAL Microsoft.Extensions for compatibility - will replace with custom config later -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <!-- Whisper.net packages for local AI transcription -->
    <PackageReference Include="Whisper.net" Version="1.7.0" />
    <PackageReference Include="Whisper.net.Runtime" Version="1.7.0" />
    <!-- Windows Forms for system tray icon (NotifyIcon) -->
    <FrameworkReference Include="Microsoft.WindowsDesktop.App" />
    <!-- RESTORED: Local Whisper AI processing capability
         - Whisper.net (15-25MB) - Local speech-to-text transcription
         - Whisper.net.Runtime (15-25MB) - Native runtime components
         NOTE: This increases app size by ~15-25MB but enables offline transcription
    -->

    <!-- TARGET: NAudio (2-5MB) + HTTP APIs + WPF + minimal config = ~8-12MB total -->
  </ItemGroup>

  <!-- TRIMMER CONFIGURATION: Specify exactly what to include -->
  <ItemGroup>
    <TrimmerRootDescriptor Include="TrimmerRoots.xml" />
  </ItemGroup>



  <ItemGroup>
    <Resource Include="powervoice-modern.ico" />
    <!-- Whisper model files for local AI transcription -->
    <None Update="ModelAssets\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Release.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <DependentUpon>appsettings.json</DependentUpon>
    </None>
    <None Update="appsettings.Production.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <DependentUpon>appsettings.json</DependentUpon>
    </None>
  </ItemGroup>

  <ItemGroup>
    <None Include=".github\copilot-instructions.md" />
  </ItemGroup>

  <!-- Custom target to ensure all dependencies are copied -->
  <Target Name="CopyAllDependencies" BeforeTargets="Build" DependsOnTargets="ResolveAssemblyReferences">
    <ItemGroup>
      <AllDependencies Include="@(ReferenceCopyLocalPaths)" />
    </ItemGroup>
    <Copy SourceFiles="@(AllDependencies)" DestinationFolder="$(OutputPath)" SkipUnchangedFiles="true" />
  </Target>

  <!-- Force copy of all package references -->
  <Target Name="ForcePackageCopy" AfterTargets="ResolvePackageAssets">
    <ItemGroup>
      <RuntimeTargetsCopyLocalItems Include="@(RuntimeTargetsCopyLocalItems)" Condition="'%(RuntimeTargetsCopyLocalItems.AssetType)' == 'runtime'" />
    </ItemGroup>
  </Target>

  <!-- Copy Google Cloud service account file to output directory -->
  <Target Name="CopyServiceAccountFile" AfterTargets="Build">
    <ItemGroup>
      <ServiceAccountFile Include="StaticResources\gen-lang-client-**********-0fb8d4808394.json" />
    </ItemGroup>
    <MakeDir Directories="$(OutputPath)StaticResources" />
    <Copy SourceFiles="@(ServiceAccountFile)" DestinationFolder="$(OutputPath)StaticResources" SkipUnchangedFiles="true" />
  </Target>
</Project>
