<Window x:Class="PowerVoice.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PowerVoice"
        xmlns:controls="clr-namespace:PowerVoice.Controls"
        mc:Ignorable="d"
        Title="PowerVoice Settings"
        Height="500" Width="480"
        MinHeight="500" MinWidth="480"
        MaxHeight="500" MaxWidth="480"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        MouseLeftButtonDown="Window_MouseLeftButtonDown"
        Icon="powervoice-modern.ico">

    <Window.Resources>
        <!-- Match Main App Color Palette -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#6366F1"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#8B5CF6"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#EC4899"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#F8FAFC"/>
        <SolidColorBrush x:Key="OnSurfaceBrush" Color="#0F172A"/>
        <SolidColorBrush x:Key="SubtleBrush" Color="#64748B"/>

        <!-- Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#8B5CF6" Offset="0"/>
            <GradientStop Color="#6366F1" Offset="0.5"/>
            <GradientStop Color="#3B82F6" Offset="1"/>
        </LinearGradientBrush>

        <!-- Button Styles -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                x:Name="BorderElement">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="BorderElement" Property="Background" Value="#5B21B6"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="BorderElement" Property="Background" Value="#4C1D95"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8"
                                x:Name="BorderElement">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="BorderElement" Property="Background" Value="#F9FAFB"/>
                                <Setter TargetName="BorderElement" Property="BorderBrush" Value="#9CA3AF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="BorderElement" Property="Background" Value="#F3F4F6"/>
                                <Setter TargetName="BorderElement" Property="BorderBrush" Value="#6B7280"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Container with Modern Glass Effect -->
    <Border Background="White"
            CornerRadius="16"
            BorderThickness="1"
            BorderBrush="#E2E8F0">
        <Border.Effect>
            <DropShadowEffect Color="#000000"
                            Direction="270"
                            ShadowDepth="8"
                            Opacity="0.15"
                            BlurRadius="24"/>
        </Border.Effect>

        <Grid Margin="24">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header with Microphone Icon -->
            <Grid Grid.Row="0" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- Microphone Icon (matching main app) -->
                    <Border Width="40" Height="40"
                            Background="{StaticResource PrimaryGradient}"
                            CornerRadius="12"
                            Margin="0,0,16,0">
                        <Path Fill="White"
                              Width="20" Height="20"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Data="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"
                              Stretch="Uniform"/>
                    </Border>

                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="PowerVoice Settings"
                                  FontSize="20"
                                  FontWeight="Bold"
                                  Foreground="{StaticResource OnSurfaceBrush}"/>
                        <TextBlock Text="Configure your dictation preferences"
                                  FontSize="13"
                                  Foreground="{StaticResource SubtleBrush}"
                                  Margin="0,4,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- Close Button -->
                <Button Grid.Column="1"
                        Content="✕"
                        Width="32" Height="32"
                        Background="Transparent"
                        BorderThickness="0"
                        FontSize="16"
                        Foreground="{StaticResource SubtleBrush}"
                        Click="Cancel_Click"
                        VerticalAlignment="Top"/>
            </Grid>

            <!-- Settings Content -->
            <StackPanel Grid.Row="1">

                <!-- General Settings -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="General"
                              FontSize="16"
                              FontWeight="SemiBold"
                              Foreground="{StaticResource OnSurfaceBrush}"
                              Margin="0,0,0,12"/>

                    <Border Background="{StaticResource SurfaceBrush}"
                            CornerRadius="12"
                            Padding="16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Start with Windows"
                                          FontSize="14"
                                          FontWeight="Medium"
                                          Foreground="{StaticResource OnSurfaceBrush}"/>
                                <TextBlock Text="Automatically launch PowerVoice when Windows starts"
                                          FontSize="12"
                                          Foreground="{StaticResource SubtleBrush}"
                                          Margin="0,2,0,0"/>
                            </StackPanel>

                            <CheckBox x:Name="LoadAtStartupCheckBox"
                                     Grid.Column="1"
                                     VerticalAlignment="Center"
                                     Checked="LoadAtStartup_Checked"
                                     Unchecked="LoadAtStartup_Unchecked"/>
                        </Grid>
                    </Border>
                </StackPanel>

                <!-- Hotkey Settings -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="Hotkey"
                              FontSize="16"
                              FontWeight="SemiBold"
                              Foreground="{StaticResource OnSurfaceBrush}"
                              Margin="0,0,0,12"/>

                    <Border Background="{StaticResource SurfaceBrush}"
                            CornerRadius="12"
                            Padding="16">
                        <controls:HotkeySelector x:Name="HotkeySelector"
                                               HotkeyChanged="HotkeySelector_HotkeyChanged"/>
                    </Border>
                </StackPanel>

                <!-- Recognition Mode -->
                <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="Recognition Mode"
                                  FontSize="16"
                                  FontWeight="SemiBold"
                                  Foreground="{StaticResource OnSurfaceBrush}"
                                  Margin="0,0,0,12"/>

                        <!-- First Row: Fast Mode (spanning full width) -->
                        <Border x:Name="FastModeCard"
                                Background="White"
                                CornerRadius="12"
                                BorderThickness="2"
                                BorderBrush="#22C55E"
                                Padding="20"
                                Margin="0,0,0,8"
                                MouseLeftButtonDown="FastMode_Click"
                                Cursor="Hand">
                            <StackPanel>
                                <Grid Margin="0,0,0,12">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Ellipse Grid.Column="0"
                                            Width="12" Height="12"
                                            Fill="#22C55E"
                                            Margin="0,0,12,0"/>

                                    <TextBlock Grid.Column="1"
                                              Text="Fast Mode"
                                              FontSize="15"
                                              FontWeight="SemiBold"
                                              Foreground="#1F2937"
                                              VerticalAlignment="Center"/>

                                    <RadioButton x:Name="FastModeRadio"
                                               Grid.Column="2"
                                               GroupName="TranscriptionMode"
                                               IsChecked="True"
                                               VerticalAlignment="Center"/>
                                </Grid>

                                <TextBlock Text="Cloud-based transcription with higher accuracy and ~1 second processing"
                                          FontSize="13"
                                          Foreground="#6B7280"
                                          TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Second Row: Three Whisper Models -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Whisper Tiny -->
                            <Border x:Name="WhisperTinyModeCard"
                                    Grid.Column="0"
                                    Background="White"
                                    CornerRadius="12"
                                    BorderThickness="1"
                                    BorderBrush="#E5E7EB"
                                    Padding="16"
                                    Margin="0,0,4,0"
                                    MouseLeftButtonDown="WhisperTinyMode_Click"
                                    Cursor="Hand">
                                <StackPanel>
                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Ellipse Grid.Column="0" Grid.Row="0"
                                                Width="10" Height="10"
                                                Fill="#3B82F6"
                                                Margin="0,0,8,0"/>

                                        <TextBlock Grid.Column="1" Grid.Row="0"
                                                  Text="Whisper Tiny"
                                                  FontSize="13"
                                                  FontWeight="SemiBold"
                                                  Foreground="#1F2937"
                                                  VerticalAlignment="Center"/>

                                        <RadioButton x:Name="WhisperTinyModeRadio"
                                                   Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2"
                                                   GroupName="TranscriptionMode"
                                                   VerticalAlignment="Center"
                                                   Margin="0,4,0,0"/>
                                    </Grid>

                                    <TextBlock Text="Fastest • 39MB"
                                              FontSize="11"
                                              Foreground="#6B7280"
                                              TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>

                            <!-- Whisper Base -->
                            <Border x:Name="WhisperBaseModeCard"
                                    Grid.Column="1"
                                    Background="White"
                                    CornerRadius="12"
                                    BorderThickness="1"
                                    BorderBrush="#E5E7EB"
                                    Padding="16"
                                    Margin="2,0,2,0"
                                    MouseLeftButtonDown="WhisperBaseMode_Click"
                                    Cursor="Hand">
                                <StackPanel>
                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Ellipse Grid.Column="0" Grid.Row="0"
                                                Width="10" Height="10"
                                                Fill="#3B82F6"
                                                Margin="0,0,8,0"/>

                                        <TextBlock Grid.Column="1" Grid.Row="0"
                                                  Text="Whisper Base"
                                                  FontSize="13"
                                                  FontWeight="SemiBold"
                                                  Foreground="#1F2937"
                                                  VerticalAlignment="Center"/>

                                        <RadioButton x:Name="WhisperBaseModeRadio"
                                                   Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2"
                                                   GroupName="TranscriptionMode"
                                                   VerticalAlignment="Center"
                                                   Margin="0,4,0,0"/>
                                    </Grid>

                                    <TextBlock Text="Balanced • 141MB"
                                              FontSize="11"
                                              Foreground="#6B7280"
                                              TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>

                            <!-- Whisper Small -->
                            <Border x:Name="WhisperSmallModeCard"
                                    Grid.Column="2"
                                    Background="White"
                                    CornerRadius="12"
                                    BorderThickness="1"
                                    BorderBrush="#E5E7EB"
                                    Padding="16"
                                    Margin="4,0,0,0"
                                    MouseLeftButtonDown="WhisperSmallMode_Click"
                                    Cursor="Hand">
                                <StackPanel>
                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Ellipse Grid.Column="0" Grid.Row="0"
                                                Width="10" Height="10"
                                                Fill="#3B82F6"
                                                Margin="0,0,8,0"/>

                                        <TextBlock Grid.Column="1" Grid.Row="0"
                                                  Text="Whisper Small"
                                                  FontSize="13"
                                                  FontWeight="SemiBold"
                                                  Foreground="#1F2937"
                                                  VerticalAlignment="Center"/>

                                        <RadioButton x:Name="WhisperSmallModeRadio"
                                                   Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2"
                                                   GroupName="TranscriptionMode"
                                                   VerticalAlignment="Center"
                                                   Margin="0,4,0,0"/>
                                    </Grid>

                                    <TextBlock Text="Best Quality • 465MB"
                                              FontSize="11"
                                              Foreground="#6B7280"
                                              TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- Mode Description -->
                        <Border Background="#F8FAFC"
                                CornerRadius="8"
                                Padding="16"
                                Margin="0,16,0,0">
                            <TextBlock x:Name="ModeDescriptionText"
                                      Text="Cloud Mode: Cloud-based transcription with higher accuracy and ~1 second processing. Perfect for commands and precise dictation."
                                      FontSize="13"
                                      Foreground="#6B7280"
                                      TextWrapping="Wrap"/>
                        </Border>
                    </StackPanel>

                    <!-- Hidden ComboBox for backward compatibility -->
                    <ComboBox x:Name="TranscriptionModeComboBox"
                             Visibility="Collapsed"
                             SelectionChanged="TranscriptionMode_SelectionChanged">
                        <ComboBoxItem Content="Cloud Transcription" Tag="FastTranscription" IsSelected="True"/>
                        <ComboBoxItem Content="Whisper Tiny" Tag="WhisperTiny"/>
                        <ComboBoxItem Content="Whisper Base" Tag="WhisperBase"/>
                        <ComboBoxItem Content="Whisper Small" Tag="WhisperSmall"/>
                    </ComboBox>

                </StackPanel>

            <!-- Footer Actions -->
            <Grid Grid.Row="2" Margin="0,20,0,0">
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Right">
                    <Button Content="Cancel"
                           Style="{StaticResource SecondaryButton}"
                           Margin="0,0,12,0"
                           Click="Cancel_Click"/>

                    <Button Content="Apply"
                           Style="{StaticResource ModernButton}"
                           Click="Save_Click"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
