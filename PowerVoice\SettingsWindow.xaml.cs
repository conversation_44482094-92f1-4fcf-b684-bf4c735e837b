using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Configuration;
using Microsoft.Win32;
using PowerVoice.Extensions;
using WpfApplication = System.Windows.Application;
using WpfCheckBox = System.Windows.Controls.CheckBox;
using WpfComboBox = System.Windows.Controls.ComboBox;
using WpfRadioButton = System.Windows.Controls.RadioButton;
using WpfTextBlock = System.Windows.Controls.TextBlock;

namespace PowerVoice
{
    public partial class SettingsWindow : Window
    {
        private const string STARTUP_REGISTRY_KEY = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run";
        private const string APP_NAME = "PowerVoice";

        // Track the currently selected mode in the UI (not yet applied)
        private string _pendingSelectedMode = "FastTranscription";

        public SettingsWindow()
        {
            InitializeComponent();
            Loaded += SettingsWindow_Loaded;
        }

        private void SettingsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            LoadCurrentSettings();
        }

        private void LoadCurrentSettings()
        {
            try
            {
                // Check if the app is set to load at startup
                var loadAtStartupCheckBox = this.FindName("LoadAtStartupCheckBox") as WpfCheckBox;
                if (loadAtStartupCheckBox != null)
                {
                    loadAtStartupCheckBox.IsChecked = IsLoadAtStartupEnabled();
                }

                // Load transcription mode from configuration
                LoadTranscriptionMode();

                DebugT.WriteLine($"✅ Settings loaded - Load at startup: {loadAtStartupCheckBox?.IsChecked}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error loading settings: {ex.Message}");
                System.Windows.MessageBox.Show($"Error loading settings: {ex.Message}", "Settings Error",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void LoadTranscriptionMode()
        {
            try
            {
                // Force reload configuration from file to get latest changes
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                string currentMode = "FastTranscription"; // Default

                DebugT.WriteLine($"🔄 Loading transcription mode from: {configPath}");

                if (File.Exists(configPath))
                {
                    var configJson = await File.ReadAllTextAsync(configPath);
                    DebugT.WriteLine($"📄 Config file content preview: {configJson.Substring(0, Math.Min(200, configJson.Length))}...");

                    var configDoc = JsonDocument.Parse(configJson);

                    if (configDoc.RootElement.TryGetProperty("AzureSpeech", out var azureSpeechElement))
                    {
                        DebugT.WriteLine("✅ Found AzureSpeech section");
                        if (azureSpeechElement.TryGetProperty("TranscriptionMode", out var modeElement))
                        {
                            currentMode = modeElement.GetString() ?? "FastTranscription";
                            DebugT.WriteLine($"✅ Found TranscriptionMode in config: {currentMode}");
                        }
                        else
                        {
                            DebugT.WriteLine("⚠️ TranscriptionMode property not found in AzureSpeech section");
                        }
                    }
                    else
                    {
                        DebugT.WriteLine("⚠️ AzureSpeech section not found in config");
                    }
                }
                else
                {
                    DebugT.WriteLine("⚠️ Configuration file does not exist");
                }

                DebugT.WriteLine($"🔄 Final mode to display: {currentMode}");

                // Set the ComboBox selection based on current mode
                var transcriptionModeComboBox = this.FindName("TranscriptionModeComboBox") as WpfComboBox;
                if (transcriptionModeComboBox != null)
                {
                    foreach (ComboBoxItem item in transcriptionModeComboBox.Items)
                    {
                        if (item.Tag?.ToString() == currentMode)
                        {
                            transcriptionModeComboBox.SelectedItem = item;
                            break;
                        }
                    }
                }

                // Update the card UI to reflect the current mode - but don't apply changes
                SelectModeVisualOnly(currentMode);

                // Initialize the pending mode with the current mode
                _pendingSelectedMode = currentMode;

                DebugT.WriteLine($"✅ Loaded and displayed transcription mode: {currentMode}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error loading transcription mode: {ex.Message}");
                DebugT.WriteLine($"   Stack trace: {ex.StackTrace}");
                // Default to FastTranscription if there's an error
                var transcriptionModeComboBox = this.FindName("TranscriptionModeComboBox") as WpfComboBox;
                if (transcriptionModeComboBox != null)
                {
                    transcriptionModeComboBox.SelectedIndex = 0;
                }
                // Default to Fast mode visually
                SelectModeVisualOnly("FastTranscription");

                // Initialize pending mode to default
                _pendingSelectedMode = "FastTranscription";
            }
        }

        private IConfiguration LoadConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            return builder.Build();
        }

        private void UpdateModeDescription(string mode)
        {
            // Check if the UI element is initialized before trying to update it
            var modeDescriptionText = this.FindName("ModeDescriptionText") as WpfTextBlock;
            if (modeDescriptionText == null)
            {
                DebugT.WriteLine("⚠️ ModeDescriptionText is null - UI not fully initialized yet");
                return;
            }

            switch (mode)
            {
                case "FastTranscription":
                    modeDescriptionText.Text = "Fast Mode: Cloud-based transcription with higher accuracy and ~1 second processing. Perfect for commands and precise dictation.";
                    break;
                case "WhisperTiny":
                    modeDescriptionText.Text = "Whisper Tiny: Fastest local transcription using tiny.en model (39MB). Good for quick commands with decent accuracy.";
                    break;
                case "WhisperBase":
                    modeDescriptionText.Text = "Whisper Base: Balanced local transcription using base.en model (141MB). Great compromise between speed and accuracy.";
                    break;
                case "WhisperSmall":
                    modeDescriptionText.Text = "Whisper Small: High-quality local transcription using small.en model (465MB). Best accuracy for detailed dictation.";
                    break;
                case "Whisper": // Legacy support
                    modeDescriptionText.Text = "Whisper Small: High-quality local transcription using small.en model (465MB). Best accuracy for detailed dictation.";
                    break;
                default:
                    modeDescriptionText.Text = "Select a recognition mode above to see details about its capabilities and recommended use cases.";
                    break;
            }
        }

        private void TranscriptionMode_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Check if UI is fully initialized before processing selection changes
            var transcriptionModeComboBox = this.FindName("TranscriptionModeComboBox") as WpfComboBox;
            if (transcriptionModeComboBox?.SelectedItem is ComboBoxItem selectedItem)
            {
                string mode = selectedItem.Tag?.ToString() ?? "FastTranscription";
                UpdateModeDescription(mode);
                DebugT.WriteLine($"🔄 Transcription mode changed to: {mode}");
            }
        }

        private bool IsLoadAtStartupEnabled()
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(STARTUP_REGISTRY_KEY, false);
                var value = key?.GetValue(APP_NAME);
                bool isEnabled = value != null;
                DebugT.WriteLine($"🔍 Startup registry check: {(isEnabled ? "Found" : "Not found")} - Value: {value}");
                return isEnabled;
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error checking startup registry: {ex.Message}");
                return false;
            }
        }

        private void SetLoadAtStartup(bool enable)
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(STARTUP_REGISTRY_KEY, true);
                if (key == null)
                {
                    DebugT.WriteLine("❌ Could not open startup registry key");
                    return;
                }

                if (enable)
                {
                    // Get the executable path
                    string exePath = Assembly.GetExecutingAssembly().Location;
                    if (string.IsNullOrEmpty(exePath))
                    {
                        exePath = Process.GetCurrentProcess().MainModule?.FileName ?? "";
                    }

                    // For .NET applications, we might need to get the actual exe path
                    if (exePath.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
                    {
                        string directory = Path.GetDirectoryName(exePath) ?? "";
                        string exeName = Path.GetFileNameWithoutExtension(exePath) + ".exe";
                        exePath = Path.Combine(directory, exeName);
                    }

                    if (File.Exists(exePath))
                    {
                        key.SetValue(APP_NAME, $"\"{exePath}\"");
                        DebugT.WriteLine($"✅ Added to startup: {exePath}");
                    }
                    else
                    {
                        DebugT.WriteLine($"❌ Executable not found: {exePath}");
                        System.Windows.MessageBox.Show("Could not find the application executable to add to startup.",
                                      "Startup Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }
                else
                {
                    key.DeleteValue(APP_NAME, false);
                    DebugT.WriteLine("✅ Removed from startup");
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Error setting startup: {ex.Message}");
                System.Windows.MessageBox.Show($"Failed to update startup setting: {ex.Message}",
                              "Settings Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadAtStartup_Checked(object sender, RoutedEventArgs e)
        {
            // Handle in Save_Click for consistency
        }

        private void LoadAtStartup_Unchecked(object sender, RoutedEventArgs e)
        {
            // Handle in Save_Click for consistency
        }

        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DebugT.WriteLine("💾 Applying final settings...");

                // Save load at startup setting
                var loadAtStartupCheckBox = this.FindName("LoadAtStartupCheckBox") as WpfCheckBox;
                bool loadAtStartup = loadAtStartupCheckBox?.IsChecked ?? false;
                SetLoadAtStartup(loadAtStartup);

                // Apply the selected transcription mode
                await ApplyPendingTranscriptionMode();

                DebugT.WriteLine("✅ All settings applied");

                // Close the window
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Error applying settings: {ex.Message}");
                System.Windows.MessageBox.Show($"Failed to apply settings: {ex.Message}",
                              "Settings Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ApplyPendingTranscriptionMode()
        {
            try
            {
                DebugT.WriteLine($"📋 Applying pending transcription mode: {_pendingSelectedMode}");

                // Save the mode to configuration
                SaveTranscriptionModeToConfig(_pendingSelectedMode);

                // Notify the main application of the change
                await NotifyMainApplicationOfModeChange(_pendingSelectedMode);

                DebugT.WriteLine($"✅ Transcription mode applied successfully: {_pendingSelectedMode}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Error applying transcription mode: {ex.Message}");
                throw; // Re-throw so Save_Click can handle it
            }
        }

        private void SaveTranscriptionMode()
        {
            try
            {
                var transcriptionModeComboBox = this.FindName("TranscriptionModeComboBox") as WpfComboBox;
                if (transcriptionModeComboBox?.SelectedItem is ComboBoxItem selectedItem)
                {
                    string selectedMode = selectedItem.Tag?.ToString() ?? "FastTranscription";

                    // Update the appsettings.json file
                    string configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                    if (File.Exists(configPath))
                    {
                        string json = File.ReadAllText(configPath);

                        // This is a simple approach - in production, you'd want more robust JSON handling
                        string updatedJson = json.Replace(
                            "\"TranscriptionMode\": \"FastTranscription\"",
                            $"\"TranscriptionMode\": \"{selectedMode}\"");

                        updatedJson = updatedJson.Replace(
                            "\"TranscriptionMode\": \"Whisper\"",
                            $"\"TranscriptionMode\": \"{selectedMode}\"");

                        File.WriteAllText(configPath, updatedJson);
                        DebugT.WriteLine($"✅ Saved transcription mode: {selectedMode}");
                    }
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error saving transcription mode: {ex.Message}");
                throw; // Re-throw to be handled by the main Save_Click method
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DebugT.WriteLine("❌ Settings cancelled");
            DialogResult = false;
            Close();
        }

        // New card-based mode selection event handlers
        private void FastMode_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            SelectMode("FastTranscription");
        }

        private void WhisperTinyMode_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            SelectMode("WhisperTiny");
        }

        private void WhisperBaseMode_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            SelectMode("WhisperBase");
        }

        private void WhisperSmallMode_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            SelectMode("WhisperSmall");
        }

        // Legacy support
        private void WhisperMode_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            SelectMode("WhisperSmall"); // Map legacy "Whisper" to "WhisperSmall"
        }

        private void SelectMode(string mode)
        {
            try
            {
                // Store the selected mode for later application
                _pendingSelectedMode = mode;

                // Update only the visual state - do NOT apply the changes yet
                SelectModeVisualOnly(mode);

                DebugT.WriteLine($"🎨 Mode selected in UI (not yet applied): {mode}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error selecting mode: {ex.Message}");
            }
        }
        private async Task ApplyModeChangeImmediately(string mode)
        {
            try
            {
                // Save the mode change immediately when selected
                SaveTranscriptionModeToConfig(mode);

                // Notify the main application if it's running
                await NotifyMainApplicationOfModeChange(mode);

                DebugT.WriteLine($"✅ Mode applied immediately: {mode}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error applying mode change: {ex.Message}");
            }
        }

        private void SaveTranscriptionModeToConfig(string mode)
        {
            try
            {
                // Update the appsettings.json file using proper JSON manipulation
                string configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                if (File.Exists(configPath))
                {
                    // Read the current configuration
                    string json = File.ReadAllText(configPath);
                    var configDoc = JsonDocument.Parse(json);

                    // Create a new configuration object
                    var updatedConfig = new Dictionary<string, object>();

                    // Copy all existing properties
                    foreach (var property in configDoc.RootElement.EnumerateObject())
                    {
                        if (property.Name == "AzureSpeech")
                        {
                            // Handle AzureSpeech section specially to update TranscriptionMode
                            var azureSpeechConfig = new Dictionary<string, object>();
                            foreach (var azureProperty in property.Value.EnumerateObject())
                            {
                                if (azureProperty.Name == "TranscriptionMode")
                                {
                                    azureSpeechConfig[azureProperty.Name] = mode;
                                }
                                else
                                {
                                    // Copy the value as-is
                                    azureSpeechConfig[azureProperty.Name] = CopyJsonElement(azureProperty.Value);
                                }
                            }
                            updatedConfig[property.Name] = azureSpeechConfig;
                        }
                        else
                        {
                            // Copy other sections as-is
                            updatedConfig[property.Name] = CopyJsonElement(property.Value);
                        }
                    }

                    // Serialize back to JSON with proper formatting
                    var options = new JsonSerializerOptions
                    {
                        WriteIndented = true
                    };
                    string updatedJson = JsonSerializer.Serialize(updatedConfig, options);
                    File.WriteAllText(configPath, updatedJson);

                    DebugT.WriteLine($"✅ Configuration updated with JSON manipulation: {mode}");
                }
                else
                {
                    DebugT.WriteLine("⚠️ Configuration file not found");
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error saving configuration: {ex.Message}");
            }
        }

        private static object CopyJsonElement(JsonElement element)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.Object:
                    var dict = new Dictionary<string, object>();
                    foreach (var prop in element.EnumerateObject())
                    {
                        dict[prop.Name] = CopyJsonElement(prop.Value);
                    }
                    return dict;

                case JsonValueKind.Array:
                    return element.EnumerateArray().Select(CopyJsonElement).ToArray();

                case JsonValueKind.String:
                    return element.GetString() ?? "";

                case JsonValueKind.Number:
                    if (element.TryGetInt32(out int intValue))
                        return intValue;
                    if (element.TryGetDouble(out double doubleValue))
                        return doubleValue;
                    return element.GetDecimal();

                case JsonValueKind.True:
                    return true;

                case JsonValueKind.False:
                    return false;

                case JsonValueKind.Null:
                    return null!;

                default:
                    return element.ToString();
            }
        }

        private async Task NotifyMainApplicationOfModeChange(string mode)
        {
            try
            {
                // Find the main window and notify it of the mode change
                var mainWindow = WpfApplication.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    // Call the UpdateTranscriptionMode method on the main window
                    // This ensures the change is applied immediately without restarting
                    await mainWindow.UpdateTranscriptionMode(mode);
                    DebugT.WriteLine($"✅ Main application notified of mode change: {mode}");
                }
                else
                {
                    DebugT.WriteLine("⚠️ Main window not found for mode change notification");
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error notifying main application: {ex.Message}");
            }
        }

        // Add a method to handle window dragging (since we removed the title bar)
        private void Window_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (e.ButtonState == System.Windows.Input.MouseButtonState.Pressed)
            {
                this.DragMove();
            }
        }

        private void SelectModeVisualOnly(string mode)
        {
            try
            {
                var fastModeCard = this.FindName("FastModeCard") as Border;
                var whisperTinyModeCard = this.FindName("WhisperTinyModeCard") as Border;
                var whisperBaseModeCard = this.FindName("WhisperBaseModeCard") as Border;
                var whisperSmallModeCard = this.FindName("WhisperSmallModeCard") as Border;
                var fastModeRadio = this.FindName("FastModeRadio") as WpfRadioButton;
                var whisperTinyModeRadio = this.FindName("WhisperTinyModeRadio") as WpfRadioButton;
                var whisperBaseModeRadio = this.FindName("WhisperBaseModeRadio") as WpfRadioButton;
                var whisperSmallModeRadio = this.FindName("WhisperSmallModeRadio") as WpfRadioButton;
                var transcriptionModeComboBox = this.FindName("TranscriptionModeComboBox") as WpfComboBox;

                // Define colors
                var selectedColor = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromArgb(255, 59, 130, 246)); // Blue for selected
                var greenColor = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromArgb(255, 34, 197, 94)); // Green for Fast
                var unselectedColor = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromArgb(255, 229, 231, 235)); // Gray for unselected

                // Reset all cards to unselected state first
                if (fastModeCard != null)
                {
                    fastModeCard.BorderBrush = unselectedColor;
                    fastModeCard.BorderThickness = new Thickness(1);
                }
                if (whisperTinyModeCard != null)
                {
                    whisperTinyModeCard.BorderBrush = unselectedColor;
                    whisperTinyModeCard.BorderThickness = new Thickness(1);
                }
                if (whisperBaseModeCard != null)
                {
                    whisperBaseModeCard.BorderBrush = unselectedColor;
                    whisperBaseModeCard.BorderThickness = new Thickness(1);
                }
                if (whisperSmallModeCard != null)
                {
                    whisperSmallModeCard.BorderBrush = unselectedColor;
                    whisperSmallModeCard.BorderThickness = new Thickness(1);
                }

                // Reset all radio buttons
                if (fastModeRadio != null) fastModeRadio.IsChecked = false;
                if (whisperTinyModeRadio != null) whisperTinyModeRadio.IsChecked = false;
                if (whisperBaseModeRadio != null) whisperBaseModeRadio.IsChecked = false;
                if (whisperSmallModeRadio != null) whisperSmallModeRadio.IsChecked = false;

                // Set the appropriate card and radio button based on mode
                if (mode == "FastTranscription")
                {
                    if (fastModeCard != null)
                    {
                        fastModeCard.BorderBrush = greenColor;
                        fastModeCard.BorderThickness = new Thickness(2);
                    }
                    if (fastModeRadio != null) fastModeRadio.IsChecked = true;
                    if (transcriptionModeComboBox != null)
                    {
                        transcriptionModeComboBox.SelectedIndex = 0;
                    }
                }
                else if (mode == "WhisperTiny")
                {
                    if (whisperTinyModeCard != null)
                    {
                        whisperTinyModeCard.BorderBrush = selectedColor;
                        whisperTinyModeCard.BorderThickness = new Thickness(2);
                    }
                    if (whisperTinyModeRadio != null) whisperTinyModeRadio.IsChecked = true;
                    if (transcriptionModeComboBox != null)
                    {
                        transcriptionModeComboBox.SelectedIndex = 1;
                    }
                }
                else if (mode == "WhisperBase")
                {
                    if (whisperBaseModeCard != null)
                    {
                        whisperBaseModeCard.BorderBrush = selectedColor;
                        whisperBaseModeCard.BorderThickness = new Thickness(2);
                    }
                    if (whisperBaseModeRadio != null) whisperBaseModeRadio.IsChecked = true;
                    if (transcriptionModeComboBox != null)
                    {
                        transcriptionModeComboBox.SelectedIndex = 2;
                    }
                }
                else if (mode == "WhisperSmall")
                {
                    if (whisperSmallModeCard != null)
                    {
                        whisperSmallModeCard.BorderBrush = selectedColor;
                        whisperSmallModeCard.BorderThickness = new Thickness(2);
                    }
                    if (whisperSmallModeRadio != null) whisperSmallModeRadio.IsChecked = true;
                    if (transcriptionModeComboBox != null)
                    {
                        transcriptionModeComboBox.SelectedIndex = 3;
                    }
                }
                else if (mode == "Whisper") // Legacy support - maps to WhisperSmall
                {
                    if (whisperSmallModeCard != null)
                    {
                        whisperSmallModeCard.BorderBrush = selectedColor;
                        whisperSmallModeCard.BorderThickness = new Thickness(2);
                    }
                    if (whisperSmallModeRadio != null) whisperSmallModeRadio.IsChecked = true;
                    if (transcriptionModeComboBox != null)
                    {
                        transcriptionModeComboBox.SelectedIndex = 3;
                    }
                }

                // Update description only - do NOT apply changes
                UpdateModeDescription(mode);

                DebugT.WriteLine($"🎨 Mode visual state updated: {mode}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error updating mode visual state: {ex.Message}");
            }
        }
    }
}
