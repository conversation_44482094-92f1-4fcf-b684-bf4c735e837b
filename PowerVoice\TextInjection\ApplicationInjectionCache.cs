using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;

namespace PowerVoice.TextInjection
{
    /// <summary>
    /// Ultra-fast in-memory cache for application text injection strategies
    /// Optimized for small datasets with automatic growth beyond 20 entries
    /// Uses hybrid approach: array for ≤20 entries, List for dynamic growth
    /// </summary>
    public static class ApplicationInjectionCache
    {
        // Hybrid storage: array for optimal performance up to 20 entries, then List for growth
        private static AppInjectionCacheEntry[] _fastCache = new AppInjectionCacheEntry[20];
        private static List<AppInjectionCacheEntry>? _dynamicCache = null;
        private static int _fastCacheCount = 0;
        private static bool _usingDynamicCache = false;

        /// <summary>
        /// Cache entry optimized for minimal memory footprint and fast access
        /// </summary>
        private struct AppInjectionCacheEntry
        {
            public string ApplicationName { get; set; }    // 8 bytes reference
            public TextInjectionMethod Method { get; set; } // 4 bytes enum
            public int SuccessCount { get; set; }          // 4 bytes
            public int FailureCount { get; set; }          // 4 bytes
            public long LastUsedTicks { get; set; }        // 8 bytes (DateTime.Ticks)
            // Total: 28 bytes per entry + string allocation
        }

        /// <summary>
        /// Get total entry count across both cache types
        /// </summary>
        private static int EntryCount => _usingDynamicCache ? _dynamicCache!.Count : _fastCacheCount;

        /// <summary>
        /// Get entry by index across both cache types
        /// </summary>
        private static AppInjectionCacheEntry GetEntry(int index)
        {
            return _usingDynamicCache ? _dynamicCache![index] : _fastCache[index];
        }

        /// <summary>
        /// Set entry by index across both cache types
        /// </summary>
        private static void SetEntry(int index, AppInjectionCacheEntry entry)
        {
            if (_usingDynamicCache)
            {
                _dynamicCache![index] = entry;
            }
            else
            {
                _fastCache[index] = entry;
            }
        }

        /// <summary>
        /// Move entry to front for LRU optimization (hybrid cache-aware)
        /// </summary>
        private static void MoveEntryToFront(int index, AppInjectionCacheEntry entry)
        {
            if (_usingDynamicCache)
            {
                // Remove from current position and insert at front
                _dynamicCache!.RemoveAt(index);
                _dynamicCache.Insert(0, entry);
            }
            else
            {
                // Array-based shifting (fast for small datasets)
                Array.Copy(_fastCache, 0, _fastCache, 1, index);
                _fastCache[0] = entry;
            }
        }

        /// <summary>
        /// Get the optimal injection method for an application
        /// Simple logic: return the method that worked last for this application
        /// O(n) linear search with LRU optimization - moves recently used entries to front
        /// </summary>
        /// <param name="applicationName">Application executable name (e.g., "notepad.exe")</param>
        /// <returns>Cached method or null if not found</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static TextInjectionMethod? GetOptimalMethod(string applicationName)
        {
            if (string.IsNullOrEmpty(applicationName) || EntryCount == 0)
            {
                return null;
            }

            string lowerAppName = applicationName.ToLowerInvariant();
            int entryCount = EntryCount;

            // Find the entry in cache
            for (int i = 0; i < entryCount; i++)
            {
                var entry = GetEntry(i);
                if (string.Equals(entry.ApplicationName, lowerAppName, StringComparison.Ordinal))
                {
                    // Simple check: only use if this method has worked at least once
                    if (entry.SuccessCount == 0)
                    {
                        Debug.WriteLine($"🔍 Cache entry found but no successes yet: {applicationName} → {entry.Method}");
                        return null;
                    }

                    // Update last used time
                    entry.LastUsedTicks = DateTime.UtcNow.Ticks;
                    SetEntry(i, entry);

                    // LRU optimization: Move to front if not already there
                    if (i > 0)
                    {
                        MoveEntryToFront(i, entry);
                        Debug.WriteLine($"💾 Cache HIT + LRU: {applicationName} → {entry.Method} (moved to front, worked {entry.SuccessCount} times)");
                    }
                    else
                    {
                        Debug.WriteLine($"💾 Cache HIT: {applicationName} → {entry.Method} (worked {entry.SuccessCount} times)");
                    }

                    return entry.Method;
                }
            }

            Debug.WriteLine($"🔍 Cache MISS: {applicationName} (will try all methods)");
            return null;
        }

        /// <summary>
        /// Update cache with successful injection method
        /// Automatically promotes successful methods and includes LRU optimization
        /// </summary>
        /// <param name="applicationName">Application executable name</param>
        /// <param name="method">Injection method that worked</param>
        /// <param name="wasSuccessful">Whether the injection succeeded</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void UpdateMethodResult(string applicationName, TextInjectionMethod method, bool wasSuccessful)
        {
            if (string.IsNullOrEmpty(applicationName))
            {
                return;
            }

            string lowerAppName = applicationName.ToLowerInvariant();
            long currentTicks = DateTime.UtcNow.Ticks;

            // Try to update existing entry
            if (TryUpdateExistingEntry(lowerAppName, method, wasSuccessful, currentTicks))
            {
                return;
            }

            // Add new entry (with automatic growth support)
            AddNewEntry(lowerAppName, method, wasSuccessful, currentTicks, applicationName);
        }

        private static bool TryUpdateExistingEntry(string lowerAppName, TextInjectionMethod method, bool wasSuccessful, long currentTicks)
        {
            int entryCount = EntryCount;
            for (int i = 0; i < entryCount; i++)
            {
                var entry = GetEntry(i);
                if (string.Equals(entry.ApplicationName, lowerAppName, StringComparison.Ordinal))
                {
                    UpdateExistingCacheEntry(i, entry, method, wasSuccessful, currentTicks, lowerAppName);
                    return true;
                }
            }
            return false;
        }

        private static void UpdateExistingCacheEntry(int index, AppInjectionCacheEntry entry, TextInjectionMethod method, bool wasSuccessful, long currentTicks, string appName)
        {
            // Simple logic: if method worked, update to use this method next time
            if (wasSuccessful)
            {
                entry.SuccessCount++;
                entry.Method = method; // Always update to the successful method
                entry.LastUsedTicks = currentTicks;
                SetEntry(index, entry);

                // LRU: Move successful entry to front if not already there
                if (index > 0)
                {
                    MoveEntryToFront(index, entry);
                    Debug.WriteLine($"✅ Cache UPDATE + LRU: {appName} → {method} (moved to front, worked {entry.SuccessCount} times)");
                }
                else
                {
                    Debug.WriteLine($"✅ Cache UPDATE: {appName} → {method} (worked {entry.SuccessCount} times)");
                }
            }
            else
            {
                // Method failed - just track it but don't change the preferred method
                entry.FailureCount++;
                entry.LastUsedTicks = currentTicks;
                SetEntry(index, entry);
                Debug.WriteLine($"❌ Cache UPDATE: {appName} → {method} failed (preferred method still: {entry.Method})");
            }
        }

        /// <summary>
        /// Add new entry with automatic growth from array to List when needed
        /// </summary>
        private static void AddNewEntry(string lowerAppName, TextInjectionMethod method, bool wasSuccessful, long currentTicks, string originalAppName)
        {
            var newEntry = new AppInjectionCacheEntry
            {
                ApplicationName = lowerAppName,
                Method = method,
                SuccessCount = wasSuccessful ? 1 : 0,
                FailureCount = wasSuccessful ? 0 : 1,
                LastUsedTicks = currentTicks
            };

            if (!_usingDynamicCache)
            {
                // Still using fast array cache
                if (_fastCacheCount < _fastCache.Length)
                {
                    // Add to array
                    _fastCache[_fastCacheCount] = newEntry;
                    _fastCacheCount++;
                    Debug.WriteLine($"➕ Cache ADD (array): {originalAppName} → {method} (success: {(wasSuccessful ? 1 : 0)}, total entries: {_fastCacheCount})");
                }
                else
                {
                    // Transition to dynamic cache
                    TransitionToDynamicCache();
                    _dynamicCache!.Add(newEntry);
                    Debug.WriteLine($"🔄 Cache TRANSITION: {originalAppName} → {method} (moved to List, total entries: {_dynamicCache.Count})");
                }
            }
            else
            {
                // Already using dynamic cache
                _dynamicCache!.Add(newEntry);
                Debug.WriteLine($"➕ Cache ADD (list): {originalAppName} → {method} (success: {(wasSuccessful ? 1 : 0)}, total entries: {_dynamicCache.Count})");
            }
        }

        /// <summary>
        /// Transition from array to List when we exceed 20 entries
        /// </summary>
        private static void TransitionToDynamicCache()
        {
            _dynamicCache = new List<AppInjectionCacheEntry>(_fastCache.Take(_fastCacheCount));
            _usingDynamicCache = true;
            Debug.WriteLine($"🚀 Cache transitioned to dynamic List (was: {_fastCacheCount} entries)");
        }

        /// <summary>
        /// Get cache statistics for monitoring and debugging
        /// </summary>
        public static CacheStatistics GetStatistics()
        {
            var stats = new CacheStatistics
            {
                TotalEntries = EntryCount,
                MaxFastCacheCapacity = _fastCache.Length,
                IsUsingDynamicCache = _usingDynamicCache,
                Entries = new List<CacheEntryInfo>()
            };

            int entryCount = EntryCount;
            for (int i = 0; i < entryCount; i++)
            {
                var entry = GetEntry(i);
                double successRate = entry.SuccessCount + entry.FailureCount > 0
                    ? (double)entry.SuccessCount / (entry.SuccessCount + entry.FailureCount)
                    : 0.0;

                stats.Entries.Add(new CacheEntryInfo
                {
                    ApplicationName = entry.ApplicationName,
                    Method = entry.Method,
                    SuccessCount = entry.SuccessCount,
                    FailureCount = entry.FailureCount,
                    SuccessRate = successRate,
                    LastUsed = new DateTime(entry.LastUsedTicks, DateTimeKind.Utc),
                    IsReliable = successRate >= 0.8 && entry.SuccessCount > 0
                });
            }

            // Sort by last used (most recent first)
            stats.Entries = stats.Entries.OrderByDescending(e => e.LastUsed).ToList();
            return stats;
        }

        /// <summary>
        /// Clear all cache entries (for testing or reset)
        /// </summary>
        public static void Clear()
        {
            if (_usingDynamicCache)
            {
                _dynamicCache?.Clear();
                _dynamicCache = null;
                _usingDynamicCache = false;
            }

            _fastCacheCount = 0;
            Array.Clear(_fastCache, 0, _fastCache.Length);
            Debug.WriteLine("🧹 Application injection cache cleared");
        }

        /// <summary>
        /// Cache statistics for monitoring and debugging
        /// </summary>
        public class CacheStatistics
        {
            public int TotalEntries { get; set; }
            public int MaxFastCacheCapacity { get; set; }
            public bool IsUsingDynamicCache { get; set; }
            public List<CacheEntryInfo> Entries { get; set; } = new List<CacheEntryInfo>();

            public double FastCacheUsagePercentage => MaxFastCacheCapacity > 0 ? (double)Math.Min(TotalEntries, MaxFastCacheCapacity) / MaxFastCacheCapacity * 100 : 0;
            public int ReliableEntries => Entries.Count(e => e.IsReliable);
        }

        /// <summary>
        /// Individual cache entry information for debugging
        /// </summary>
        public class CacheEntryInfo
        {
            public string ApplicationName { get; set; } = string.Empty;
            public TextInjectionMethod Method { get; set; }
            public int SuccessCount { get; set; }
            public int FailureCount { get; set; }
            public double SuccessRate { get; set; }
            public DateTime LastUsed { get; set; }
            public bool IsReliable { get; set; }

            public int TotalAttempts => SuccessCount + FailureCount;
        }
    }
}
