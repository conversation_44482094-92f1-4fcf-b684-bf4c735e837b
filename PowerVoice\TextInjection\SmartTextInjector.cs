using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Automation;
using PowerVoice.Extensions;

namespace PowerVoice.TextInjection
{
    /// <summary>
    /// Text injection method type for caching
    /// </summary>
    public enum TextInjectionMethod
    {
        UIAutomationValuePattern,
        UIAutomationTextPattern,
        OptimizedSendKeys,
        OfficeSendInput,
        WordUltraFast,
        OfficeDirectCharacter,
        OfficeWindowsMessages,
        CharByChar
    }

    /// <summary>
    /// Application-specific text injection strategy cache entry
    /// Simple storage: just remember what method worked last for each application
    /// </summary>
    public class AppInjectionStrategy
    {
        public string ApplicationName { get; set; } = string.Empty;
        public TextInjectionMethod PreferredMethod { get; set; }
        public DateTime LastUsed { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public double AverageSpeed { get; set; } // Characters per second
    }

    /// <summary>
    /// Smart text injection service that uses the fastest available method for each application
    /// Prioritizes UI Automation for maximum performance with compatible applications
    /// Features adaptive caching to remember the best method for each application
    /// </summary>
    public class SmartTextInjector
    {
        // Windows API imports for fallback methods
        [DllImport("user32.dll")]
        private static extern bool PostMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        // Windows message constants
        private const uint WM_CHAR = 0x0102;

        // Additional Windows API imports for advanced Office methods
        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

        [DllImport("kernel32.dll")]
        private static extern uint GetCurrentThreadId();

        [DllImport("user32.dll")]
        private static extern bool AttachThreadInput(uint idAttach, uint idAttachTo, bool fAttach);

        [DllImport("user32.dll")]
        private static extern short VkKeyScan(char ch);

        [DllImport("user32.dll")]
        private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);

        [DllImport("user32.dll")]
        private static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, string lParam);

        [DllImport("user32.dll")]
        private static extern IntPtr GetFocusedWindow();

        // Additional Windows message constants
        private const uint KEYEVENTF_KEYUP = 0x0002;

        // High-speed SendInput API for maximum performance
        [DllImport("user32.dll")]
        private static extern uint SendInput(uint nInputs, INPUT[] pInputs, int cbSize);

        // SendInput structures for ultra-fast text injection
        private const int INPUT_KEYBOARD = 1;
        private const uint KEYEVENTF_UNICODE = 0x0004;

        [StructLayout(LayoutKind.Sequential)]
        public struct INPUT
        {
            public int type;
            public InputUnion U;
        }

        [StructLayout(LayoutKind.Explicit)]
        public struct InputUnion
        {
            [FieldOffset(0)]
            public KEYBDINPUT ki;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct KEYBDINPUT
        {
            public ushort wVk;
            public ushort wScan;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        /// <summary>
        /// Inject text using the fastest available method with intelligent caching
        /// </summary>
        /// <param name="text">Text to inject</param>
        /// <returns>Performance metrics and success status</returns>
        public async Task<TextInjectionResult> InjectTextAsync(string text)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                DebugT.WriteLine($"🚀 Smart Text Injection: Starting injection of {text.Length} characters");

                // Get the application name for cache lookup
                string applicationName = await GetCurrentApplicationNameAsync();
                DebugT.WriteLine($"🎯 Target application: {applicationName}");

                // Check cache for optimal method
                var cachedMethod = ApplicationInjectionCache.GetOptimalMethod(applicationName);
                if (cachedMethod.HasValue)
                {
                    DebugT.WriteLine($"💾 Cache HIT: Using {cachedMethod.Value} for {applicationName}");
                    var cachedResult = await TrySpecificMethodAsync(text, cachedMethod.Value);

                    // Update cache with result
                    ApplicationInjectionCache.UpdateMethodResult(applicationName, cachedMethod.Value, cachedResult.Success);

                    if (cachedResult.Success)
                    {
                        stopwatch.Stop();
                        DebugT.WriteLine($"✅ Cached method {cachedMethod.Value} succeeded in {stopwatch.ElapsedMilliseconds} ms");
                        return new TextInjectionResult
                        {
                            Success = true,
                            Method = $"{cachedMethod.Value} (Cached)",
                            ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                            CharactersPerSecond = (int)(text.Length / stopwatch.Elapsed.TotalSeconds),
                            Message = cachedResult.Message
                        };
                    }
                    else
                    {
                        DebugT.WriteLine($"⚠️ Cached method {cachedMethod.Value} failed, trying other methods...");
                    }
                }

                // Cache miss or cached method failed - try all methods
                var result = await TryAllMethodsAsync(text, applicationName);

                stopwatch.Stop();
                return new TextInjectionResult
                {
                    Success = result.Success,
                    Method = result.Method,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                    CharactersPerSecond = result.Success ? (int)(text.Length / stopwatch.Elapsed.TotalSeconds) : 0,
                    Message = result.Message
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                DebugT.WriteLine($"❌ Smart Text Injection error: {ex.Message}");
                return new TextInjectionResult
                {
                    Success = false,
                    Method = "Error",
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                    CharactersPerSecond = 0,
                    Message = $"Error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Try UI Automation with ValuePattern and TextPattern (fastest method)
        /// Enhanced for Office applications and complex text controls
        /// </summary>
        private static async Task<(bool Success, string Message)> TryUIAutomationAsync(string text)
        {
            try
            {
                DebugT.WriteLine("🔄 Attempting UI Automation method...");

                return await Task.Run(() => ProcessUIAutomation(text));
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ UI Automation failed: {ex.Message}");
                return (false, $"UI Automation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Process UI Automation with focused element detection and app-specific handling
        /// </summary>
        private static (bool Success, string Message) ProcessUIAutomation(string text)
        {
            try
            {
                var focusedElement = AutomationElement.FocusedElement;
                if (focusedElement == null)
                {
                    DebugT.WriteLine("❌ No focused element found");
                    return (false, "No focused element found");
                }

                LogFocusedElementInfo(focusedElement);
                string processName = GetProcessName(focusedElement);

                // Try application-specific optimizations first
                var appResult = TryApplicationSpecificHandling(focusedElement, text, processName);
                if (appResult.Success)
                {
                    return appResult;
                }

                // Fall back to generic UI Automation patterns
                return TryGenericUIAutomationPatterns(focusedElement, text);
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ UI Automation inner error: {ex.Message}");
                return (false, $"UI Automation error: {ex.Message}");
            }
        }

        /// <summary>
        /// Log information about the focused element
        /// </summary>
        private static void LogFocusedElementInfo(AutomationElement focusedElement)
        {
            string elementInfo = $"{focusedElement.Current.Name} ({focusedElement.Current.ControlType.LocalizedControlType})";
            DebugT.WriteLine($"🎯 Found focused element: {elementInfo}");
        }

        /// <summary>
        /// Try application-specific handling for Office apps
        /// </summary>
        private static (bool Success, string Message) TryApplicationSpecificHandling(AutomationElement focusedElement, string text, string processName)
        {
            bool isOfficeApp = IsOfficeApplication(processName);

            if (isOfficeApp)
            {
                DebugT.WriteLine($"🏢 Detected Office application: {processName}");
                return TryOfficeSpecificUIAutomation(focusedElement, text, processName);
            }

            return (false, "No application-specific handling available");
        }

        /// <summary>
        /// Try generic UI Automation patterns (ValuePattern and TextPattern)
        /// </summary>
        private static (bool Success, string Message) TryGenericUIAutomationPatterns(AutomationElement focusedElement, string text)
        {
            // Try ValuePattern first
            var valuePatternResult = TryValuePatternInjection(focusedElement, text);
            if (valuePatternResult.Success)
            {
                return valuePatternResult;
            }

            // Try TextPattern as fallback
            if (focusedElement.TryGetCurrentPattern(TextPattern.Pattern, out _))
            {
                DebugT.WriteLine("🔄 ValuePattern not available, trying TextPattern...");
                return TryTextPatternInjection(text);
            }

            DebugT.WriteLine("❌ No compatible UI Automation pattern found");
            return (false, "No compatible UI Automation pattern found");
        }

        /// <summary>
        /// Try ValuePattern for text injection with cursor-safe approach
        /// </summary>
        private static (bool Success, string Message) TryValuePatternInjection(AutomationElement focusedElement, string text)
        {
            if (!focusedElement.TryGetCurrentPattern(ValuePattern.Pattern, out object? valuePatternObj))
            {
                return (false, "ValuePattern not available");
            }

            var valuePattern = (ValuePattern)valuePatternObj;
            if (valuePattern.Current.IsReadOnly)
            {
                DebugT.WriteLine("❌ Text control is read-only");
                return (false, "Text control is read-only");
            }

            return ExecuteCursorSafeSendKeys(text);
        }

        /// <summary>
        /// Execute cursor-safe SendKeys method for text injection with performance optimization
        /// Uses chunked approach based on Microsoft recommended practices for better speed
        /// </summary>
        private static (bool Success, string Message) ExecuteCursorSafeSendKeys(string text)
        {
            try
            {
                DebugT.WriteLine("🔄 Using OPTIMIZED CURSOR-SAFE ValuePattern method");
                DebugT.WriteLine("⚡ Using OPTIMIZED chunked SendKeys for ValuePattern application");

                EnsureTargetWindowFocus();

                // Use optimized chunked SendKeys for better performance while maintaining cursor safety
                bool success = TryOptimizedChunkedSendKeys(text);
                if (success)
                {
                    DebugT.WriteLine($"✅ OPTIMIZED chunked SendKeys succeeded for {text.Length} characters");
                    return (true, "Optimized chunked SendKeys at cursor position succeeded");
                }
                else
                {
                    DebugT.WriteLine("⚠️ Chunked SendKeys failed, falling back to standard SendKeys");
                    // Fallback to standard SendKeys
                    System.Windows.Forms.SendKeys.SendWait(text);
                    DebugT.WriteLine($"✅ Standard SendKeys fallback succeeded for {text.Length} characters");
                    return (true, "Standard SendKeys fallback succeeded");
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Optimized SendKeys method failed: {ex.Message}");
                return (false, $"Optimized SendKeys method failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Ensure target window has proper focus before text injection with optimized timing
        /// </summary>
        private static void EnsureTargetWindowFocus()
        {
            IntPtr targetWindow = GetForegroundWindow();
            if (targetWindow != IntPtr.Zero)
            {
                SetForegroundWindow(targetWindow);
                // Optimized delay: 5ms is sufficient for window focus, 10ms was unnecessarily slow
                System.Threading.Thread.Sleep(5);
            }
        }

        /// <summary>
        /// Office-specific UI Automation handling for Word, Excel, PowerPoint, Outlook
        /// Uses PowerVoice's advanced 5-strategy approach for maximum performance
        /// </summary>
        private static (bool Success, string Message) TryOfficeSpecificUIAutomation(AutomationElement element, string text, string processName)
        {
            try
            {
                DebugT.WriteLine($"🏢 Using PowerVoice advanced Office strategies for: {processName}");
                string windowInfo = $"{processName}.exe";

                // Strategy 1: Try ultra-fast SendInput for Word (like VS Code optimization)
                if (processName == "winword")
                {
                    DebugT.WriteLine($"📝 Detected Word - trying ultra-fast SendInput API");
                    var wordResult = TryWordUltraFastSendInput(text);
                    if (wordResult.Success)
                    {
                        return wordResult;
                    }
                    DebugT.WriteLine("⚠️ Word ultra-fast SendInput failed, trying fallback methods...");
                }

                // Strategy 2: Try SendInput method (most reliable for modern Office)
                if (TryOfficeSendInputMethod(text))
                {
                    return (true, $"SendInput method succeeded for {processName}");
                }

                // Strategy 2: Try TextPattern with proper Office handling
                if (TryOfficeTextPatternMethod(element, text, processName))
                {
                    return (true, $"TextPattern method succeeded for {processName}");
                }

                // Strategy 3: Try direct character injection with Office-optimized timing
                if (TryOfficeDirectCharacterMethod(text, windowInfo))
                {
                    return (true, $"Direct character method succeeded for {processName}");
                }

                // Strategy 4: Try Windows messages with WM_CHAR
                if (TryOfficeWindowsMessages(text, windowInfo))
                {
                    return (true, $"Windows messages method succeeded for {processName}");
                }

                return (false, $"All Office strategies failed for {processName}");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Office-specific strategies error: {ex.Message}");
                return (false, $"Office strategies error: {ex.Message}");
            }
        }

        /// <summary>
        /// Use TextPattern for complex text controls (rich text editors, code editors)
        /// CRITICAL: This method properly respects cursor position
        /// </summary>
        private static (bool Success, string Message) TryTextPatternInjection(string text)
        {
            try
            {
                DebugT.WriteLine("📍 Using TextPattern to insert text at current cursor position");

                // Ensure the target window has proper focus before sending keys
                IntPtr targetWindow = GetForegroundWindow();
                if (targetWindow != IntPtr.Zero)
                {
                    SetForegroundWindow(targetWindow);
                    System.Threading.Thread.Sleep(10); // Small delay to ensure focus
                }

                // Method 1: Use SendKeys to insert at current cursor position (most reliable)
                // This is the SAFEST method that respects cursor position in ALL applications
                System.Windows.Forms.SendKeys.SendWait(text);
                DebugT.WriteLine($"✅ TextPattern with SendKeys succeeded for {text.Length} characters");
                return (true, "TextPattern with SendKeys at cursor position succeeded");

                // NOTE: We deliberately avoid using TextPatternRange.Select() and document manipulation
                // because those methods can move the cursor to unexpected locations
                // SendKeys.SendWait() always inserts at the current cursor position
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ TextPattern injection error: {ex.Message}");
                return (false, $"TextPattern error: {ex.Message}");
            }
        }

        /// <summary>
        /// Get the process name of the application containing the automation element
        /// </summary>
        private static string GetProcessName(AutomationElement element)
        {
            try
            {
                int processId = element.Current.ProcessId;
                using var process = System.Diagnostics.Process.GetProcessById(processId);
                return process.ProcessName.ToLowerInvariant();
            }
            catch
            {
                return "unknown";
            }
        }

        /// <summary>
        /// Check if the application is a Microsoft Office application
        /// </summary>
        private static bool IsOfficeApplication(string processName)
        {
            return processName switch
            {
                "winword" => true,      // Microsoft Word
                "excel" => true,        // Microsoft Excel
                "powerpnt" => true,     // Microsoft PowerPoint
                "outlook" => true,      // Microsoft Outlook
                "onenote" => true,      // Microsoft OneNote
                "teams" => true,        // Microsoft Teams
                "msaccess" => true,     // Microsoft Access
                _ => false
            };
        }

        /// <summary>
        /// Fallback method using WM_CHAR messages (compatible with most applications, BUT NOT OFFICE)
        /// NOTE: This method does NOT work reliably with Microsoft Office applications
        /// </summary>
        private static async Task<(bool Success, string Message)> TryCharByCharMethodAsync(string text)
        {
            try
            {
                DebugT.WriteLine("🔄 Attempting character-by-character method...");

                return await Task.Run(() =>
                {
                    try
                    {
                        // Get the foreground window
                        IntPtr foregroundWindow = GetForegroundWindow();
                        if (foregroundWindow == IntPtr.Zero)
                        {
                            DebugT.WriteLine("❌ No foreground window found");
                            return (false, "No foreground window found");
                        }

                        // Check if this is an Office application - WM_CHAR doesn't work reliably with Office
                        string windowTitle = GetWindowTitle(foregroundWindow);
                        if (IsOfficeWindow(windowTitle))
                        {
                            DebugT.WriteLine("⚠️ Office application detected - WM_CHAR method not reliable for Office");
                            return (false, "WM_CHAR method not reliable for Office applications");
                        }

                        DebugT.WriteLine($"📝 Sending {text.Length} characters to window {foregroundWindow} (Title: {windowTitle})...");

                        // Ensure window focus for better reliability
                        SetForegroundWindow(foregroundWindow);
                        System.Threading.Thread.Sleep(5);

                        // Send each character individually at maximum speed
                        int successCount = 0;
                        foreach (char c in text)
                        {
                            // Skip control characters that might cause issues
                            if (char.IsControl(c) && c != '\r' && c != '\n' && c != '\t')
                                continue;

                            PostMessage(foregroundWindow, WM_CHAR, new IntPtr(c), IntPtr.Zero);
                            successCount++;
                            // No delay for maximum speed - 100+ chars/ms possible
                        }

                        DebugT.WriteLine($"✅ Character-by-character injection completed: {successCount}/{text.Length} characters sent");
                        return (true, $"WM_CHAR method succeeded - {successCount} characters sent");
                    }
                    catch (Exception ex)
                    {
                        DebugT.WriteLine($"❌ Character-by-character method error: {ex.Message}");
                        return (false, $"WM_CHAR error: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Character-by-character method failed: {ex.Message}");
                return (false, $"WM_CHAR failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Try SendInput-based injection for Office applications (most reliable method)
        /// </summary>
        private static bool TryOfficeSendInputMethod(string text)
        {
            try
            {
                DebugT.WriteLine($"⌨️ Trying OPTIMIZED SendInput method for Office");

                IntPtr targetWindow = GetForegroundWindow();
                if (targetWindow == IntPtr.Zero) return false;

                SetForegroundWindow(targetWindow);
                System.Threading.Thread.Sleep(50); // Small delay to ensure focus

                // Attach to the target thread for better focus control
                uint targetThreadId = GetWindowThreadProcessId(targetWindow, out uint _);
                uint currentThreadId = GetCurrentThreadId();

                bool attached = false;
                if (targetThreadId != currentThreadId)
                {
                    attached = AttachThreadInput(currentThreadId, targetThreadId, true);
                }

                try
                {
                    // ULTRA-FAST character injection - match Wispr Flow speed
                    foreach (char c in text)
                    {
                        // Skip control characters except common ones
                        if (char.IsControl(c) && c != '\r' && c != '\n' && c != '\t')
                            continue;

                        // Convert problematic characters
                        char charToSend = c;
                        if (c == '\r' || c == '\n' || c == '\t')
                        {
                            charToSend = ' ';
                        }

                        // Get virtual key code
                        short vkAndShift = VkKeyScan(charToSend);
                        if (vkAndShift == -1)
                        {
                            vkAndShift = VkKeyScan(' ');
                        }

                        if (vkAndShift == -1) continue;

                        byte vk = (byte)(vkAndShift & 0xFF);
                        byte shiftState = (byte)((vkAndShift >> 8) & 0xFF);
                        bool needsShift = (shiftState & 1) != 0;

                        // ULTRA-FAST shift handling - minimal delays
                        if (needsShift)
                        {
                            keybd_event(0x10, 0, 0, UIntPtr.Zero); // VK_SHIFT down
                            System.Threading.Thread.Sleep(2); // Reduced from 10ms to 2ms
                        }

                        keybd_event(vk, 0, 0, UIntPtr.Zero); // Key down
                        System.Threading.Thread.Sleep(3); // Reduced from 15ms to 3ms
                        keybd_event(vk, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // Key up

                        if (needsShift)
                        {
                            System.Threading.Thread.Sleep(2); // Reduced from 10ms to 2ms
                            keybd_event(0x10, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_SHIFT up
                        }

                        // System.Threading.Thread.Sleep(5); // DRAMATICALLY reduced from 25ms to 5ms
                    }

                    DebugT.WriteLine($"✅ OPTIMIZED SendInput completed for {text.Length} characters");
                    return true;
                }
                finally
                {
                    if (attached)
                    {
                        AttachThreadInput(currentThreadId, targetThreadId, false);
                    }
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ SendInput method error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Try TextPattern method optimized for Office applications
        /// CRITICAL: This method properly respects cursor position
        /// </summary>
        private static bool TryOfficeTextPatternMethod(AutomationElement element, string text, string processName)
        {
            try
            {
                if (element.TryGetCurrentPattern(TextPattern.Pattern, out object? textPatternObj))
                {
                    var textPattern = (TextPattern)textPatternObj;
                    DebugT.WriteLine($"📄 Using cursor-safe TextPattern for Office: {processName}");

                    // CRITICAL FIX: Do NOT manipulate text selection or document ranges
                    // as this can move the cursor to unexpected locations

                    // Use SendKeys to insert at current cursor position (most reliable)
                    // This is the SAFEST method that respects cursor position in ALL applications
                    System.Windows.Forms.SendKeys.SendWait(text);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ TextPattern method error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Direct character injection method optimized for Office applications
        /// </summary>
        private static bool TryOfficeDirectCharacterMethod(string text, string windowInfo)
        {
            try
            {
                DebugT.WriteLine($"🔤 Trying direct character method for Office");

                // Get target window and ensure focus
                IntPtr targetWindow = GetForegroundWindow();
                if (targetWindow == IntPtr.Zero) return false;

                SetForegroundWindow(targetWindow);
                // No delay for maximum speed

                // Ultra-fast timing - maximum speed
                int modifierDelay = 0;      // No modifier delays
                int keyPressDelay = 0;      // No key press delay

                int successCount = 0;
                int charIndex = 0;

                foreach (char c in text)
                {
                    charIndex++;
                    try
                    {
                        // Skip control characters except common ones
                        if (char.IsControl(c) && c != '\r' && c != '\n' && c != '\t')
                        {
                            continue;
                        }

                        // Convert problematic characters
                        char charToSend = c;
                        if (c == '\r' || c == '\n' || c == '\t')
                        {
                            charToSend = ' ';
                        }

                        // Get virtual key code
                        short vkAndShift = VkKeyScan(charToSend);
                        if (vkAndShift == -1)
                        {
                            charToSend = ' ';
                            vkAndShift = VkKeyScan(' ');
                        }

                        if (vkAndShift == -1) continue;

                        byte vk = (byte)(vkAndShift & 0xFF);
                        byte shiftState = (byte)((vkAndShift >> 8) & 0xFF);
                        bool needsShift = (shiftState & 1) != 0;

                        // Send with Office-optimized timing
                        if (needsShift)
                        {
                            keybd_event(0x10, 0, 0, UIntPtr.Zero); // VK_SHIFT down
                            System.Threading.Thread.Sleep(modifierDelay);
                        }

                        keybd_event(vk, 0, 0, UIntPtr.Zero); // Key down
                        System.Threading.Thread.Sleep(keyPressDelay);
                        keybd_event(vk, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // Key up

                        if (needsShift)
                        {
                            System.Threading.Thread.Sleep(modifierDelay);
                            keybd_event(0x10, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // VK_SHIFT up
                        }

                        successCount++;
                        // No delays for maximum speed

                        // No periodic delays for maximum speed
                    }
                    catch (Exception ex)
                    {
                        DebugT.WriteLine($"❌ Failed to send character '{c}' to Office: {ex.Message}");
                    }
                }

                DebugT.WriteLine($"🏢 Office direct character injection: {successCount}/{text.Length} characters sent");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Direct character method error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Try Windows message-based text injection for Office
        /// </summary>
        private static bool TryOfficeWindowsMessages(string text, string windowInfo)
        {
            try
            {
                DebugT.WriteLine($"💬 Trying Windows messages for Office");

                IntPtr targetWindow = GetForegroundWindow();
                if (targetWindow == IntPtr.Zero) return false;

                SetForegroundWindow(targetWindow);
                System.Threading.Thread.Sleep(100);

                // Try sending WM_CHAR messages with Office-optimized timing
                int successCount = 0;
                foreach (char c in text)
                {
                    try
                    {
                        // Skip control characters
                        if (char.IsControl(c) && c != '\r' && c != '\n' && c != '\t') continue;

                        // Convert special characters
                        char charToSend = c;
                        if (c == '\r' || c == '\n' || c == '\t')
                        {
                            charToSend = ' ';
                        }

                        // Send WM_CHAR message
                        PostMessage(targetWindow, WM_CHAR, new IntPtr(charToSend), IntPtr.Zero);

                        successCount++;

                        // Office-optimized delay between characters
                        System.Threading.Thread.Sleep(5);
                    }
                    catch (Exception ex)
                    {
                        DebugT.WriteLine($"❌ Failed to send WM_CHAR '{c}': {ex.Message}");
                    }
                }

                DebugT.WriteLine($"💬 Office Windows messages: {successCount}/{text.Length} characters sent");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Windows messages error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Research-proven Word text injection using Microsoft's recommended KEYEVENTF_UNICODE method
        /// Designed specifically for "voice recognition" applications according to Microsoft docs
        /// </summary>
        private static (bool Success, string Message) TryWordUltraFastSendInput(string text)
        {
            try
            {
                DebugT.WriteLine($"📝 Using Microsoft-recommended KEYEVENTF_UNICODE for Word ({text.Length} characters)");

                // Get Word window and ensure proper focus (critical for Word)
                IntPtr targetWindow = GetForegroundWindow();
                if (targetWindow == IntPtr.Zero)
                {
                    DebugT.WriteLine("❌ No foreground window found for Word");
                    return (false, "No foreground window found");
                }

                // Attach to Word's input thread for better input processing (Microsoft recommended)
                uint targetThreadId = GetWindowThreadProcessId(targetWindow, out uint _);
                uint currentThreadId = GetCurrentThreadId();
                bool attached = false;

                if (targetThreadId != currentThreadId)
                {
                    attached = AttachThreadInput(currentThreadId, targetThreadId, true);
                    DebugT.WriteLine($"🔗 Attached to Word thread: {attached}");
                }

                try
                {
                    SetForegroundWindow(targetWindow);
                    System.Threading.Thread.Sleep(50); // Word needs this for thread synchronization

                    // Use Microsoft's proven KEYEVENTF_UNICODE method for voice recognition apps
                    var inputs = new INPUT[text.Length];

                    for (int i = 0; i < text.Length; i++)
                    {
                        inputs[i] = new INPUT
                        {
                            type = INPUT_KEYBOARD,
                            U = new InputUnion
                            {
                                ki = new KEYBDINPUT
                                {
                                    wVk = 0,                    // Must be 0 for KEYEVENTF_UNICODE
                                    wScan = (ushort)text[i],    // Unicode character
                                    dwFlags = KEYEVENTF_UNICODE, // Microsoft's method for voice recognition
                                    time = 0,                   // System timestamp
                                    dwExtraInfo = IntPtr.Zero
                                }
                            }
                        };
                    }

                    // Send all characters using Microsoft's optimized method
                    uint result = SendInput((uint)inputs.Length, inputs, Marshal.SizeOf(typeof(INPUT)));

                    // Small processing delay for Word's input buffer
                    System.Threading.Thread.Sleep(10);

                    if (result == inputs.Length)
                    {
                        DebugT.WriteLine($"✅ Microsoft KEYEVENTF_UNICODE method succeeded ({result} chars processed)");
                        return (true, "Microsoft KEYEVENTF_UNICODE method - designed for voice recognition");
                    }
                    else
                    {
                        DebugT.WriteLine($"⚠️ Partial success: {result}/{inputs.Length} characters processed");
                        return (result > 0, $"Partial success: {result}/{inputs.Length} characters");
                    }
                }
                finally
                {
                    // Detach from Word's thread
                    if (attached)
                    {
                        AttachThreadInput(currentThreadId, targetThreadId, false);
                    }
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Microsoft KEYEVENTF_UNICODE method error: {ex.Message}");
                return (false, $"KEYEVENTF_UNICODE error: {ex.Message}");
            }
        }

        // Add this helper method to SmartTextInjector class to fix CS0103
        private static string GetWindowTitle(IntPtr hWnd)
        {
            const int nChars = 256;
            var Buff = new System.Text.StringBuilder(nChars);
            if (hWnd != IntPtr.Zero)
            {
                if (GetWindowText(hWnd, Buff, nChars) > 0)
                {
                    return Buff.ToString();
                }
            }
            return string.Empty;
        }

        // Add this Windows API import to SmartTextInjector class
        [DllImport("user32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder lpString, int nMaxCount);
        // Add this helper method to SmartTextInjector class to fix CS0103
        private static bool IsOfficeWindow(string windowTitle)
        {
            if (string.IsNullOrEmpty(windowTitle))
                return false;

            // Check for common Office application window titles
            string lowerTitle = windowTitle.ToLowerInvariant();
            return lowerTitle.Contains("microsoft word") ||
                   lowerTitle.Contains("microsoft excel") ||
                   lowerTitle.Contains("microsoft powerpoint") ||
                   lowerTitle.Contains("onenote") ||
                   lowerTitle.Contains("outlook") ||
                   lowerTitle.Contains("teams") ||
                   lowerTitle.Contains("access");
        }

        /// <summary>
        /// Optimized chunked SendKeys implementation based on Microsoft recommended practices
        /// Chunks text into optimal sizes for better performance while maintaining cursor position accuracy
        /// </summary>
        private static bool TryOptimizedChunkedSendKeys(string text)
        {
            try
            {
                if (string.IsNullOrEmpty(text))
                    return true;

                // Optimal chunk size based on Microsoft recommendations and research:
                // - SendKeys has optimal performance with chunks of 64-128 characters
                // - Larger chunks can cause buffer overflows and slower processing
                // - Smaller chunks add unnecessary overhead
                const int OPTIMAL_CHUNK_SIZE = 96; // Sweet spot for performance vs reliability

                // For short text, use standard SendKeys
                if (text.Length <= OPTIMAL_CHUNK_SIZE)
                {
                    System.Windows.Forms.SendKeys.SendWait(text);
                    return true;
                }

                DebugT.WriteLine($"📦 Chunking {text.Length} characters into optimal {OPTIMAL_CHUNK_SIZE}-character chunks");

                // Process text in optimized chunks
                for (int i = 0; i < text.Length; i += OPTIMAL_CHUNK_SIZE)
                {
                    int chunkLength = Math.Min(OPTIMAL_CHUNK_SIZE, text.Length - i);
                    string chunk = text.Substring(i, chunkLength);

                    // Send chunk using SendKeys
                    System.Windows.Forms.SendKeys.SendWait(chunk);

                    // Minimal delay between chunks for buffer processing
                    // Based on Windows message queue processing speed (typical ~1-2ms)
                    if (i + OPTIMAL_CHUNK_SIZE < text.Length) // Don't delay after last chunk
                    {
                        System.Threading.Thread.Sleep(1); // Minimal 1ms delay for optimal throughput
                    }

                    DebugT.WriteLine($"📦 Sent chunk {(i / OPTIMAL_CHUNK_SIZE) + 1} of {(text.Length + OPTIMAL_CHUNK_SIZE - 1) / OPTIMAL_CHUNK_SIZE}: {chunkLength} chars");
                }

                DebugT.WriteLine($"✅ Chunked SendKeys completed: {text.Length} characters processed");
                return true;
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Chunked SendKeys failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get the current application name asynchronously
        /// </summary>
        private async Task<string> GetCurrentApplicationNameAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    IntPtr foregroundWindow = GetForegroundWindow();
                    if (foregroundWindow == IntPtr.Zero)
                        return "unknown";

                    GetWindowThreadProcessId(foregroundWindow, out uint processId);
                    using var process = System.Diagnostics.Process.GetProcessById((int)processId);
                    return process.ProcessName.ToLowerInvariant();
                }
                catch
                {
                    return "unknown";
                }
            });
        }

        /// <summary>
        /// Try a specific injection method
        /// </summary>
        private async Task<(bool Success, string Message)> TrySpecificMethodAsync(string text, TextInjectionMethod method)
        {
            // Get window info for Office methods that need it
            string windowInfo = "";
            if (method == TextInjectionMethod.OfficeDirectCharacter || method == TextInjectionMethod.OfficeWindowsMessages)
            {
                try
                {
                    IntPtr targetWindow = GetForegroundWindow();
                    if (targetWindow != IntPtr.Zero)
                    {
                        GetWindowThreadProcessId(targetWindow, out uint processId);
                        using (var process = Process.GetProcessById((int)processId))
                        {
                            windowInfo = $"{process.ProcessName}.exe";
                        }
                    }
                }
                catch (Exception ex)
                {
                    DebugT.WriteLine($"⚠️ Failed to get window info: {ex.Message}");
                    windowInfo = "unknown.exe";
                }
            }

            return method switch
            {
                TextInjectionMethod.UIAutomationValuePattern => await TryUIAutomationAsync(text),
                TextInjectionMethod.UIAutomationTextPattern => await TryUIAutomationAsync(text),
                TextInjectionMethod.OptimizedSendKeys => await TryUIAutomationAsync(text),
                TextInjectionMethod.OfficeSendInput => await Task.FromResult(TryWordUltraFastSendInput(text)),
                TextInjectionMethod.WordUltraFast => await Task.FromResult(TryWordUltraFastSendInput(text)),
                TextInjectionMethod.OfficeDirectCharacter => await Task.FromResult((TryOfficeDirectCharacterMethod(text, windowInfo), "Office Direct Character")),
                TextInjectionMethod.OfficeWindowsMessages => await Task.FromResult((TryOfficeWindowsMessages(text, windowInfo), "Office Windows Messages")),
                TextInjectionMethod.CharByChar => await TryCharByCharMethodAsync(text),
                _ => (false, $"Unknown method: {method}")
            };
        }

        /// <summary>
        /// Try all available methods and update cache with results
        /// Simple logic: try methods in default order (UI Automation first, then CharByChar)
        /// </summary>
        private async Task<(bool Success, string Method, string Message)> TryAllMethodsAsync(string text, string applicationName)
        {
            // Define methods to try in priority order (fastest first)
            var methodsToTry = new[]
            {
                (TextInjectionMethod.UIAutomationValuePattern, "UI Automation"),
                (TextInjectionMethod.WordUltraFast, "Word Ultra Fast"),
                (TextInjectionMethod.OfficeDirectCharacter, "Office Direct Character"),
                (TextInjectionMethod.OfficeWindowsMessages, "Office Windows Messages"),
                (TextInjectionMethod.OfficeSendInput, "Office SendInput"),
                (TextInjectionMethod.CharByChar, "Character-by-Character")
            };

            foreach (var (method, methodName) in methodsToTry)
            {
                try
                {
                    DebugT.WriteLine($"🔄 Trying {methodName} for {applicationName}...");
                    var result = await TrySpecificMethodAsync(text, method);

                    // Update cache with result
                    ApplicationInjectionCache.UpdateMethodResult(applicationName, method, result.Success);

                    if (result.Success)
                    {
                        DebugT.WriteLine($"✅ {methodName} succeeded for {applicationName}");
                        return (true, methodName, result.Message);
                    }

                    DebugT.WriteLine($"❌ {methodName} failed for {applicationName}: {result.Message}");
                }
                catch (Exception ex)
                {
                    DebugT.WriteLine($"❌ {methodName} threw exception for {applicationName}: {ex.Message}");
                    ApplicationInjectionCache.UpdateMethodResult(applicationName, method, false);
                }
            }

            return (false, "None", "All injection methods failed");
        }
    }

    /// <summary>
    /// Result of text injection operation
    /// </summary>
    public class TextInjectionResult
    {
        public bool Success { get; set; }
        public string Method { get; set; } = string.Empty;
        public long ElapsedMilliseconds { get; set; }
        public int CharactersPerSecond { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
