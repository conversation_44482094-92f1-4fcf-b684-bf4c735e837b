using System.Diagnostics;
using System.IO;
using Microsoft.Extensions.Configuration;
using NAudio.Wave;
using PowerVoice.AudioProcessing;
using PowerVoice.Extensions;
using PowerVoice.TranscriptionCore;

namespace PowerVoice.TranscriptionAzure
{
    /// <summary>
    /// Azure Transcription Service Adapter - Direct integration with Azure Speech Services
    /// CONTINUOUS LONG-FORM DICTATION: Uses intelligent chunking with sentence boundary detection
    /// Processes audio in chunks during natural pauses, accumulating results in memory
    /// Simplified architecture with direct Azure provider usage for optimal performance
    /// </summary>
    internal class AzureTranscriptionServiceAdapter : ILiveTranscriptionService
    {
        private readonly AzureFastTranscriptionProvider _azureService;
        private bool _disposed;
        private bool _isListening = false;

        // Real-time audio processing components
        private WaveInEvent? _waveIn;
        private StreamingMP3Buffer? _streamingMP3Buffer;
        private readonly object _lockObject = new object();

        // Voice activity detection (for UI feedback ONLY - not for processing control)
        private bool _isSpeaking = false;
        private const double VOICE_THRESHOLD = 200.0; // Lowered from 500.0 - more sensitive to speech onset

        private CancellationTokenSource? _cancellationTokenSource;

        // WARM AUDIO DEVICE: Keep audio components initialized but NOT recording for instant start
        private WaveInEvent? _warmWaveIn;
        private StreamingMP3Buffer? _warmMP3Buffer;
        private bool _isWarmSystemReady = false;

        // MEMORY OPTIMIZATION: Resource pooling to prevent allocations during ALT operations
        private WaveInEvent? _pooledWaveIn;
        private StreamingMP3Buffer? _pooledMP3Buffer;
        private CancellationTokenSource? _pooledCancellationTokenSource;
        private readonly object _poolLock = new object();

        // PRE-INITIALIZED components for INSTANT start
        private readonly WaveFormat _waveFormat = new WaveFormat(16000, 1); // Moved inline for on-demand use
        private bool _isPrepared = false;

        // CONTINUOUS CHUNKING: State management for long-form dictation
        private readonly List<string> _accumulatedText = new List<string>();
        private readonly object _chunkLock = new object();
        private DateTime _lastChunkTime = DateTime.UtcNow;
        private DateTime _sessionStartTime = DateTime.UtcNow;
        private bool _isProcessingChunk = false;
        private int _chunkCounter = 0;

        // Configuration for intelligent chunking
        private readonly int _minChunkDurationMs;
        private readonly int _maxChunkDurationMs;

        public string ServiceName => "Azure Speech Services";

        // Events
        public event EventHandler<TranscriptionEventArgs>? TranscriptionReceived;
        public event EventHandler<string>? ErrorOccurred;
        public event EventHandler? SessionStarted;
        public event EventHandler? SessionEnded;
        public event EventHandler? SpeechStarted;
        public event EventHandler? SpeechStopped;
        public event EventHandler? VoiceDetected;
        public event EventHandler? VoiceStopped;

        public AzureTranscriptionServiceAdapter(IConfiguration configuration)
        {
            _azureService = new AzureFastTranscriptionProvider(configuration ?? throw new ArgumentNullException(nameof(configuration)));
            _waveFormat = new WaveFormat(16000, 16, 1); // Pre-configure format

            // Load chunking configuration from settings
            _minChunkDurationMs = 5000;        // Minimum 5 seconds per chunk
            _maxChunkDurationMs = 45000;       // Maximum 45 seconds per chunk (safe for Azure)

            // FIXED: Don't start microphone automatically - only when Alt key is pressed
            // This matches the behavior of Whisper services where recording only starts in StartListeningAsync()
        }

        /// <summary>
        /// WARM AUDIO SYSTEM: Pre-initialize audio components but keep them ready (NOT recording) for instant start
        /// This eliminates the 122ms delay without any privacy concerns
        /// </summary>
        public async Task PreInitializeAsync()
        {
            if (_isPrepared)
            {
                DebugT.WriteLine("⚡ WARM-SYSTEM: Already prepared, skipping");
                return;
            }

            try
            {
                DebugT.WriteLine("🔥 WARM-SYSTEM: Creating instant-ready audio components (NO recording, NO privacy issues)");

                // Validate connection first
                await _azureService.ValidateConnectionAsync();

                // CREATE WARM AUDIO SYSTEM - initialized but NOT recording
                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        // Create warm WaveIn device - ready to start instantly
                        var waveFormat = new WaveFormat(16000, 16, 1);
                        _warmWaveIn = new WaveInEvent
                        {
                            WaveFormat = waveFormat,
                            BufferMilliseconds = 10, // Optimized for instant response
                            NumberOfBuffers = 3      // Optimal balance for reliability and speed
                        };

                        // Create warm MP3 buffer - ready for instant audio processing
                        _warmMP3Buffer = new StreamingMP3Buffer(waveFormat, VOICE_THRESHOLD, 800, 1200);

                        // Wire up events for instant activation (but NOT started yet)
                        _warmWaveIn.DataAvailable += OnMicrophoneDataAvailable;
                        _warmWaveIn.DataAvailable += _warmMP3Buffer.OnAudioDataAvailable;
                        _warmWaveIn.RecordingStopped += OnRecordingStopped;

                        _isWarmSystemReady = true;
                        DebugT.WriteLine("🔥 WARM-SYSTEM: Audio device ready for INSTANT start (0ms delay, NO recording)");

                        // Also create pool resources for fallback
                        var poolWaveIn = new WaveInEvent
                        {
                            WaveFormat = waveFormat,
                            BufferMilliseconds = 10,
                            NumberOfBuffers = 3
                        };
                        var poolMP3Buffer = new StreamingMP3Buffer(waveFormat, VOICE_THRESHOLD, 800, 1200);

                        lock (_poolLock)
                        {
                            _pooledWaveIn = poolWaveIn;
                            _pooledMP3Buffer = poolMP3Buffer;
                        }
                    }
                });

                _isPrepared = true;
                DebugT.WriteLine("✅ WARM-SYSTEM: Ready for ZERO-DELAY recording start (privacy-safe)");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Warm system initialization failed: {ex.Message}");
                throw;
            }
        }

        public async Task StartListeningAsync()
        {
            if (_isListening) return;

            try
            {
                DebugT.WriteLine("⚡ CONTINUOUS: Starting long-form dictation with intelligent chunking...");
                DebugT.WriteLine($"⚡ Pre-init status: _isPrepared={_isPrepared}, _waveIn={(_waveIn != null ? "Ready" : "Null")}");

                // Initialize chunking session state
                lock (_chunkLock)
                {
                    _accumulatedText.Clear();
                    _chunkCounter = 0;
                    _isProcessingChunk = false;
                    _lastChunkTime = DateTime.UtcNow;
                    _sessionStartTime = DateTime.UtcNow;
                    DebugT.WriteLine("🔄 Chunking session initialized - ready for continuous dictation");
                }

                // WARM AUDIO SYSTEM: Use pre-initialized device for INSTANT start (0ms delay)
                if (_warmWaveIn != null && _isWarmSystemReady)
                {
                    DebugT.WriteLine("🔥 WARM-START: Using pre-initialized audio device for ZERO delay");

                    // Transfer warm components to active use
                    _waveIn = _warmWaveIn;
                    _streamingMP3Buffer = _warmMP3Buffer;

                    // Clear warm references (will be recreated for next session)
                    _warmWaveIn = null;
                    _warmMP3Buffer = null;
                    _isWarmSystemReady = false;

                    // START RECORDING INSTANTLY - no initialization delay!
                    _waveIn.StartRecording();
                    DebugT.WriteLine("⚡ INSTANT: Recording started with ZERO delay!");
                }
                else if (_waveIn == null)
                {
                    DebugT.WriteLine("⚡ ON-DEMAND: Creating audio components as needed for minimal resource usage");
                    InitializeAudioRecordingSync();
                }
                else
                {
                    lock (_lockObject)
                    {
                        // CRITICAL: Ensure events are still wired after cleanup
                        if (_streamingMP3Buffer != null)
                        {
                            // Re-verify event connections (cleanup might have unwired them)
                            _waveIn.DataAvailable -= OnMicrophoneDataAvailable;
                            _waveIn.DataAvailable -= _streamingMP3Buffer.OnAudioDataAvailable;
                            _waveIn.RecordingStopped -= OnRecordingStopped;

                            // Re-wire events for this session
                            _waveIn.DataAvailable += OnMicrophoneDataAvailable;
                            _waveIn.DataAvailable += _streamingMP3Buffer.OnAudioDataAvailable;
                            _waveIn.RecordingStopped += OnRecordingStopped;

                            DebugT.WriteLine("🔌 Re-wired events for on-demand capture");
                        }

                        // START RECORDING
                        _waveIn.StartRecording();
                        DebugT.WriteLine("⚡ ON-DEMAND: Microphone started efficiently");
                    }
                }

                // Ensure buffer is attached synchronously before marking listening
                EnsureMp3BufferAttached();

                _isListening = true;

                // WARM SYSTEM: Recreate warm components for next Alt press (background task)
                if (!_isWarmSystemReady)
                {
                    _ = Task.Run(RecreateWarmSystemAsync);
                }

                // MEMORY OPTIMIZATION: Try to reuse pooled CancellationTokenSource
                lock (_poolLock)
                {
                    if (_pooledCancellationTokenSource != null)
                    {
                        _cancellationTokenSource = _pooledCancellationTokenSource;
                        _pooledCancellationTokenSource = null;
                        DebugT.WriteLine("♻️ Reusing pooled CancellationTokenSource - zero allocation");
                    }
                    else
                    {
                        _cancellationTokenSource = new CancellationTokenSource();
                        DebugT.WriteLine("🆕 Created new CancellationTokenSource (pool was empty)");
                    }
                }

                SessionStarted?.Invoke(this, EventArgs.Empty);
                DebugT.WriteLine("⚡ CONTINUOUS: Long-form dictation started - unlimited duration supported");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Continuous dictation start failed: {ex.Message}");
                ErrorOccurred?.Invoke(this, $"Failed to start continuous dictation: {ex.Message}");
                await CleanupAudioResourcesAsync();
            }
        }

        /// <summary>
        /// Recreate warm audio system in background for next Alt press (privacy-safe)
        /// </summary>
        private async Task RecreateWarmSystemAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        if (_isWarmSystemReady || _disposed) return;

                        // Create new warm components for instant next use
                        var waveFormat = new WaveFormat(16000, 16, 1);
                        _warmWaveIn = new WaveInEvent
                        {
                            WaveFormat = waveFormat,
                            BufferMilliseconds = 10,
                            NumberOfBuffers = 3
                        };

                        _warmMP3Buffer = new StreamingMP3Buffer(waveFormat, VOICE_THRESHOLD, 800, 1200);

                        // Pre-wire events for instant activation
                        _warmWaveIn.DataAvailable += OnMicrophoneDataAvailable;
                        _warmWaveIn.DataAvailable += _warmMP3Buffer.OnAudioDataAvailable;
                        _warmWaveIn.RecordingStopped += OnRecordingStopped;

                        _isWarmSystemReady = true;
                        DebugT.WriteLine("🔥 BACKGROUND: Warm system recreated for instant next Alt press");
                    }
                });
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Failed to recreate warm system: {ex.Message}");
            }
        }

        public async Task StopListeningAsync()
        {
            if (!_isListening) return;

            DebugT.WriteLine("⏹️ Continuous Dictation: Stopping and processing final chunk...");

            // Process final chunk and get accumulated results
            await ProcessFinalChunkAndReturnAccumulatedText();

            _isListening = false;
            if (_cancellationTokenSource != null)
            {
                await _cancellationTokenSource.CancelAsync();
            }

            // FIXED: Actually stop microphone recording like Whisper services
            // This ensures microphone only records when Alt key is pressed
            lock (_lockObject)
            {
                if (_waveIn != null)
                {
                    _waveIn.StopRecording();
                    DebugT.WriteLine("🛑 Microphone recording stopped - will only restart when Alt is pressed");

                    if (_streamingMP3Buffer != null)
                    {
                        _waveIn.DataAvailable -= _streamingMP3Buffer.OnAudioDataAvailable;
                    }
                }

                if (_streamingMP3Buffer != null)
                {
                    try
                    {
                        _streamingMP3Buffer.Reset();
                        lock (_poolLock)
                        {
                            if (_pooledMP3Buffer == null)
                            {
                                _pooledMP3Buffer = _streamingMP3Buffer;
                                DebugT.WriteLine("♻️ MP3 buffer returned to pool for reuse");
                            }
                            else
                            {
                                _streamingMP3Buffer.Dispose();
                                DebugT.WriteLine("🗑️ Disposed extra MP3 buffer (pool full)");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        DebugT.WriteLine($"⚠️ Error pooling MP3 buffer: {ex.Message}");
                        _streamingMP3Buffer?.Dispose();
                    }
                    _streamingMP3Buffer = null;
                }

                if (_cancellationTokenSource != null)
                {
                    try
                    {
                        if (!_cancellationTokenSource.Token.IsCancellationRequested)
                        {
                            lock (_poolLock)
                            {
                                if (_pooledCancellationTokenSource == null)
                                {
                                    _pooledCancellationTokenSource = _cancellationTokenSource;
                                    DebugT.WriteLine("♻️ CancellationTokenSource returned to pool for reuse");
                                }
                                else
                                {
                                    _cancellationTokenSource.Dispose();
                                    DebugT.WriteLine("🗑️ Disposed extra CancellationTokenSource (pool full)");
                                }
                            }
                        }
                        else
                        {
                            _cancellationTokenSource.Dispose();
                            DebugT.WriteLine("🗑️ Disposed used CancellationTokenSource");
                        }
                    }
                    catch (Exception ex)
                    {
                        DebugT.WriteLine($"⚠️ Error pooling CancellationTokenSource: {ex.Message}");
                        _cancellationTokenSource?.Dispose();
                    }
                    _cancellationTokenSource = null;
                }
            }

            SessionEnded?.Invoke(this, EventArgs.Empty);
            DebugT.WriteLine("✅ Continuous Dictation: Stopped listening");
        }

        private void InitializeAudioRecordingSync()
        {
            // MEMORY OPTIMIZATION: Use pooled resources to prevent allocations during ALT operations
            lock (_lockObject)
            {
                // CRITICAL: Clean up any existing WaveIn to prevent "Already recording" errors
                if (_waveIn != null)
                {
                    DebugT.WriteLine("🔄 Cleaning up existing WaveIn before reinitialization");
                    try
                    {
                        _waveIn.StopRecording();
                        Thread.Sleep(50); // Allow time for recording to stop
                    }
                    catch (InvalidOperationException)
                    {
                        // Expected if not recording - safe to ignore
                    }

                    // Remove all event handlers
                    _waveIn.DataAvailable -= OnMicrophoneDataAvailable;
                    _waveIn.RecordingStopped -= OnRecordingStopped;

                    _waveIn.Dispose();
                    _waveIn = null;
                    DebugT.WriteLine("✅ Previous WaveIn cleaned up successfully");
                }

                // MEMORY OPTIMIZATION: Try to reuse pooled WaveIn instance
                lock (_poolLock)
                {
                    if (_pooledWaveIn != null)
                    {
                        _waveIn = _pooledWaveIn;
                        _pooledWaveIn = null;
                        DebugT.WriteLine("♻️ Reusing pooled WaveIn instance - zero allocation");
                    }
                    else
                    {
                        // Pre-configured format for speed (no calculations)
                        var waveFormat = new WaveFormat(16000, 16, 1);

                        // OPTIMIZED: Smaller buffer for instant audio capture start
                        _waveIn = new WaveInEvent
                        {
                            WaveFormat = waveFormat,
                            BufferMilliseconds = 10, // Reduced from 20ms for faster first-word capture
                            NumberOfBuffers = 3      // Increased from 2 for better reliability
                        };
                        DebugT.WriteLine("🆕 Created new WaveIn instance (pool was empty)");
                    }
                }

                // Wire up minimal events for immediate start
                _waveIn.DataAvailable += OnMicrophoneDataAvailable;
                _waveIn.RecordingStopped += OnRecordingStopped;

                // CRITICAL FIX: Create MP3 buffer SYNCHRONOUSLY before starting recording
                // This prevents the 0.3-0.8 second delay that was causing first word loss
                lock (_poolLock)
                {
                    if (_pooledMP3Buffer != null)
                    {
                        _streamingMP3Buffer = _pooledMP3Buffer;
                        _pooledMP3Buffer = null;
                        DebugT.WriteLine("♻️ Reusing pooled MP3 buffer - zero allocation");
                    }
                    else
                    {
                        var waveFormat = new WaveFormat(16000, 16, 1);
                        // More sensitive VAD settings to prevent cutting off first words
                        _streamingMP3Buffer = new StreamingMP3Buffer(waveFormat, VOICE_THRESHOLD, 800, 1200);
                        DebugT.WriteLine("🆕 Created new MP3 buffer synchronously for instant capture");
                    }
                }

                // Add MP3 buffer event handler BEFORE starting recording
                _waveIn.DataAvailable += _streamingMP3Buffer.OnAudioDataAvailable;

                // START RECORDING IMMEDIATELY - buffer is now ready to receive data
                _waveIn.StartRecording();

                DebugT.WriteLine("⚡ OPTIMIZED: Microphone started with synchronized buffer - first word capture ready!");
            }
        }

        // Create/reuse MP3 buffer and attach to WaveIn synchronously for zero-delay capture
        private void EnsureMp3BufferAttached()
        {
            lock (_lockObject)
            {
                if (_waveIn == null) return;

                if (_streamingMP3Buffer == null)
                {
                    lock (_poolLock)
                    {
                        if (_pooledMP3Buffer != null)
                        {
                            _streamingMP3Buffer = _pooledMP3Buffer;
                            _pooledMP3Buffer = null;
                            DebugT.WriteLine("♻️ Reusing pooled MP3 buffer - zero allocation");
                        }
                        else
                        {
                            // More sensitive VAD settings to prevent cutting off first words
                            _streamingMP3Buffer = new StreamingMP3Buffer(_waveFormat, VOICE_THRESHOLD, 800, 1200);
                            DebugT.WriteLine("🆕 Created new MP3 buffer (no pool)");
                        }
                    }
                }

                // Attach handler (dedupe first)
                _waveIn.DataAvailable -= _streamingMP3Buffer!.OnAudioDataAvailable;
                _waveIn.DataAvailable += _streamingMP3Buffer!.OnAudioDataAvailable;
                DebugT.WriteLine("🔗 Attached MP3 buffer to microphone");
            }
        }

        private void OnMicrophoneDataAvailable(object? sender, WaveInEventArgs e)
        {
            if (!_isListening || e.BytesRecorded == 0) return;

            try
            {
                lock (_lockObject)
                {
                    // Validate input parameters
                    if (e.Buffer == null || e.BytesRecorded > e.Buffer.Length || e.BytesRecorded < 0)
                    {
                        DebugT.WriteLine($"⚠️ Invalid audio buffer: BytesRecorded={e.BytesRecorded}, BufferLength={e.Buffer?.Length ?? 0}");
                        return;
                    }

                    // Audio data is now handled by StreamingAudioBuffer with real-time silence filtering
                    // This method now only handles voice activity detection for UI feedback

                    // Perform voice activity detection for UI feedback ONLY
                    PerformVoiceActivityDetection(e.Buffer, e.BytesRecorded);

                    // INTELLIGENT CHUNKING: Check for sentence boundaries during continuous recording
                    CheckForIntelligentChunking();

                    // NO MANUAL PROCESSING LOGIC HERE - StreamingAudioBuffer handles real-time silence filtering
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Fast Transcription audio data error: {ex.Message}");
                ErrorOccurred?.Invoke(this, $"Audio processing error: {ex.Message}");
            }
        }

        private static void OnRecordingStopped(object? sender, StoppedEventArgs e)
        {
            DebugT.WriteLine("🛑 Fast Transcription: Recording stopped");
        }

        private void PerformVoiceActivityDetection(byte[] audioBuffer, int bytesRecorded)
        {
            // Simple voice detection for UI feedback ONLY - not used for processing control
            try
            {
                // Skip processing if buffer is too small, invalid, or empty
                if (audioBuffer == null || bytesRecorded < 8 || bytesRecorded > audioBuffer.Length)
                {
                    if (_isSpeaking)
                    {
                        _isSpeaking = false;
                        VoiceStopped?.Invoke(this, EventArgs.Empty);
                        SpeechStopped?.Invoke(this, EventArgs.Empty);
                        DebugT.WriteLine("🤐 Fast Transcription: Voice stopped (minimal audio)");
                    }
                    return;
                }

                // OPTIMIZATION: Lightweight volume-based detection with minimal CPU usage
                long totalVolume = 0;
                int processedSamples = 0;

                // Process every 16th sample for maximum performance (reduced from 8th)
                for (int i = 0; i < bytesRecorded - 1; i += 16)
                {
                    if (i + 1 < bytesRecorded)
                    {
                        // Read 16-bit sample safely with minimal processing
                        int unsignedValue = audioBuffer[i] | (audioBuffer[i + 1] << 8);
                        short signedSample = (short)(unsignedValue > 32767 ? unsignedValue - 65536 : unsignedValue);

                        totalVolume += Math.Abs(signedSample);
                        processedSamples++;
                    }
                }

                if (processedSamples == 0)
                {
                    if (_isSpeaking)
                    {
                        _isSpeaking = false;
                        VoiceStopped?.Invoke(this, EventArgs.Empty);
                        SpeechStopped?.Invoke(this, EventArgs.Empty);
                        DebugT.WriteLine("🤐 Fast Transcription: Voice stopped (no samples)");
                    }
                    return;
                }

                // Calculate average volume and update UI state
                double averageVolume = (double)totalVolume / processedSamples;
                bool wasSpeaking = _isSpeaking;
                _isSpeaking = averageVolume > (VOICE_THRESHOLD / 10) && averageVolume < 50000;

                if (_isSpeaking && !wasSpeaking)
                {
                    VoiceDetected?.Invoke(this, EventArgs.Empty);
                    SpeechStarted?.Invoke(this, EventArgs.Empty);
                    DebugT.WriteLine("🗣️ Fast Transcription: Voice detected");
                }
                else if (!_isSpeaking && wasSpeaking)
                {
                    VoiceStopped?.Invoke(this, EventArgs.Empty);
                    SpeechStopped?.Invoke(this, EventArgs.Empty);
                    DebugT.WriteLine("🤐 Fast Transcription: Voice stopped");
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Fast Transcription VAD error: {ex.Message}");
                if (_isSpeaking)
                {
                    _isSpeaking = false;
                    VoiceStopped?.Invoke(this, EventArgs.Empty);
                    SpeechStopped?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        /// <summary>
        /// Check for intelligent chunking opportunities during continuous recording
        /// Processes chunks when sentence boundaries are detected or time limits are reached
        /// </summary>
        private void CheckForIntelligentChunking()
        {
            if (_cancellationTokenSource?.Token.IsCancellationRequested == true || !_isListening) return;

            lock (_lockObject)
            {
                if (_streamingMP3Buffer == null || _isProcessingChunk) return;

                var timeSinceLastChunk = DateTime.UtcNow - _lastChunkTime;
                var sessionDuration = DateTime.UtcNow - _sessionStartTime;

                // Check conditions for chunking:
                // 1. Sentence boundary detected (1.5+ seconds of silence)
                // 2. Minimum chunk duration met (5+ seconds)
                // 3. Maximum chunk duration reached (45 seconds - safety limit)
                bool atSentenceBoundary = _streamingMP3Buffer.IsAtSentenceBoundary();
                bool minDurationMet = timeSinceLastChunk.TotalMilliseconds >= _minChunkDurationMs;
                bool maxDurationReached = timeSinceLastChunk.TotalMilliseconds >= _maxChunkDurationMs;

                bool shouldChunk = (atSentenceBoundary && minDurationMet) || maxDurationReached;

                if (shouldChunk)
                {
                    DebugT.WriteLine($"🔄 Intelligent chunking triggered:");
                    DebugT.WriteLine($"   Sentence boundary: {atSentenceBoundary}");
                    DebugT.WriteLine($"   Min duration met: {minDurationMet} ({timeSinceLastChunk.TotalSeconds:F1}s)");
                    DebugT.WriteLine($"   Max duration reached: {maxDurationReached}");
                    DebugT.WriteLine($"   Session duration: {sessionDuration.TotalSeconds:F1}s");

                    // Process current chunk in background
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await ProcessCurrentChunkAsync();
                        }
                        catch (Exception ex)
                        {
                            DebugT.WriteLine($"❌ Chunk processing error: {ex.Message}");
                        }
                    });
                }
            }
        }

        /// <summary>
        /// Process the current audio chunk and start a new one for continuous recording
        /// </summary>
        private async Task ProcessCurrentChunkAsync()
        {
            if (_cancellationTokenSource?.Token.IsCancellationRequested == true) return;

            lock (_chunkLock)
            {
                if (_isProcessingChunk) return; // Prevent concurrent processing
                _isProcessingChunk = true;
            }

            try
            {
                byte[] chunkAudioData;
                int chunkNumber = ++_chunkCounter;

                lock (_lockObject)
                {
                    if (_streamingMP3Buffer == null) return;

                    // Extract current chunk audio
                    chunkAudioData = _streamingMP3Buffer.GetCurrentChunkMP3Audio();

                    if (chunkAudioData.Length < 1000) // Skip very short chunks
                    {
                        DebugT.WriteLine($"⏭️ Skipping chunk #{chunkNumber} - too short ({chunkAudioData.Length} bytes)");
                        return;
                    }

                    // Start new chunk for continuous recording
                    _streamingMP3Buffer.StartNewChunk();
                    _lastChunkTime = DateTime.UtcNow;

                    DebugT.WriteLine($"🎯 Processing chunk #{chunkNumber}: {chunkAudioData.Length / 1024.0:F1} KB");
                }

                // Process chunk with transcription service
                var result = await _azureService.TranscribeAsync(chunkAudioData, "audio/mp3", _cancellationTokenSource?.Token ?? default);

                if (result.IsSuccess && !string.IsNullOrEmpty(result.Text))
                {
                    lock (_chunkLock)
                    {
                        _accumulatedText.Add(result.Text);
                        DebugT.WriteLine($"✅ Chunk #{chunkNumber} transcribed: \"{result.Text}\"");
                        DebugT.WriteLine($"📊 Total chunks accumulated: {_accumulatedText.Count}");
                    }
                }
                else if (!result.IsSuccess)
                {
                    DebugT.WriteLine($"❌ Chunk #{chunkNumber} transcription failed: {result.ErrorMessage}");
                }
                else
                {
                    DebugT.WriteLine($"ℹ️ Chunk #{chunkNumber}: No text recognized (silence or very short audio)");
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Error processing chunk: {ex.Message}");
            }
            finally
            {
                lock (_chunkLock)
                {
                    _isProcessingChunk = false;
                }
            }
        }

        /// <summary>
        /// Process the final chunk and return all accumulated text when Alt key is released
        /// </summary>
        private async Task ProcessFinalChunkAndReturnAccumulatedText()
        {
            try
            {
                // Wait for any ongoing chunk processing to complete
                lock (_chunkLock)
                {
                    if (_isProcessingChunk)
                    {
                        DebugT.WriteLine("⏳ Waiting for ongoing chunk processing to complete...");
                    }
                }

                // Wait up to 3 seconds for chunk processing to complete
                var maxWaitTime = DateTime.UtcNow.AddSeconds(3);
                while (_isProcessingChunk && DateTime.UtcNow < maxWaitTime)
                {
                    await Task.Delay(100);
                }

                // Process final chunk (remaining audio)
                byte[] finalAudioData;
                lock (_lockObject)
                {
                    if (_streamingMP3Buffer == null) return;

                    finalAudioData = _streamingMP3Buffer.GetFilteredMP3Audio();
                    DebugT.WriteLine($"🏁 Processing final chunk: {finalAudioData.Length / 1024.0:F1} KB");
                }

                if (finalAudioData.Length > 1000) // Only process if significant audio remains
                {
                    var result = await _azureService.TranscribeAsync(finalAudioData, "audio/mp3", _cancellationTokenSource?.Token ?? default);

                    if (result.IsSuccess && !string.IsNullOrEmpty(result.Text))
                    {
                        lock (_chunkLock)
                        {
                            _accumulatedText.Add(result.Text);
                            DebugT.WriteLine($"✅ Final chunk transcribed: \"{result.Text}\"");
                        }
                    }
                }

                // Combine all accumulated text and send as final result
                string combinedText;
                lock (_chunkLock)
                {
                    combinedText = string.Join(" ", _accumulatedText).Trim();
                    DebugT.WriteLine($"🎯 FINAL RESULT: Combined {_accumulatedText.Count} chunks into: \"{combinedText}\"");
                    DebugT.WriteLine($"📊 Session stats: {_accumulatedText.Count} chunks, {(DateTime.UtcNow - _sessionStartTime).TotalSeconds:F1}s total");
                }

                if (!string.IsNullOrEmpty(combinedText))
                {
                    // Fire transcription event with the complete accumulated result
                    var eventArgs = new TranscriptionEventArgs(combinedText, TimeSpan.FromMilliseconds((DateTime.UtcNow - _sessionStartTime).TotalMilliseconds));
                    TranscriptionReceived?.Invoke(this, eventArgs);
                    DebugT.WriteLine($"🚀 CONTINUOUS DICTATION COMPLETE: \"{combinedText}\"");
                }
                else
                {
                    DebugT.WriteLine("ℹ️ No text accumulated during session (all chunks were silent)");
                }
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Error processing final chunk: {ex.Message}");
                ErrorOccurred?.Invoke(this, $"Final chunk processing error: {ex.Message}");
            }
        }

        private async Task CleanupAudioResourcesAsync()
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    if (_waveIn != null)
                    {
                        // Properly unhook event handlers
                        _waveIn.DataAvailable -= OnMicrophoneDataAvailable;
                        if (_streamingMP3Buffer != null)
                        {
                            _waveIn.DataAvailable -= _streamingMP3Buffer.OnAudioDataAvailable;
                        }
                        _waveIn.RecordingStopped -= OnRecordingStopped;

                        _waveIn.StopRecording();

                        // MEMORY OPTIMIZATION: Return WaveIn to pool instead of disposing
                        lock (_poolLock)
                        {
                            if (_pooledWaveIn == null)
                            {
                                _pooledWaveIn = _waveIn;
                                DebugT.WriteLine("♻️ WaveIn returned to pool for reuse");
                            }
                            else
                            {
                                _waveIn.Dispose();
                                DebugT.WriteLine("🧹 Disposed extra WaveIn (pool full)");
                            }
                        }
                        _waveIn = null;
                    }

                    // MEMORY OPTIMIZATION: Return MP3 buffer to pool instead of disposing
                    if (_streamingMP3Buffer != null)
                    {
                        try
                        {
                            // Reset buffer for reuse
                            _streamingMP3Buffer.Reset();

                            lock (_poolLock)
                            {
                                if (_pooledMP3Buffer == null)
                                {
                                    _pooledMP3Buffer = _streamingMP3Buffer;
                                    DebugT.WriteLine("♻️ MP3 buffer returned to pool for reuse");
                                }
                                else
                                {
                                    _streamingMP3Buffer.Dispose();
                                    DebugT.WriteLine("🧹 Disposed extra MP3 buffer (pool full)");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            DebugT.WriteLine($"⚠️ Error pooling MP3 buffer: {ex.Message}");
                            _streamingMP3Buffer?.Dispose();
                        }
                        _streamingMP3Buffer = null;
                    }

                    // MEMORY OPTIMIZATION: Return CancellationTokenSource to pool instead of disposing
                    if (_cancellationTokenSource != null)
                    {
                        try
                        {
                            // Only pool if not cancelled
                            if (!_cancellationTokenSource.Token.IsCancellationRequested)
                            {
                                lock (_poolLock)
                                {
                                    if (_pooledCancellationTokenSource == null)
                                    {
                                        _pooledCancellationTokenSource = _cancellationTokenSource;
                                        DebugT.WriteLine("♻️ CancellationTokenSource returned to pool for reuse");
                                    }
                                    else
                                    {
                                        _cancellationTokenSource.Dispose();
                                        DebugT.WriteLine("🧹 Disposed extra CancellationTokenSource (pool full)");
                                    }
                                }
                            }
                            else
                            {
                                _cancellationTokenSource.Dispose();
                                DebugT.WriteLine("🧹 Disposed used CancellationTokenSource");
                            }
                        }
                        catch (Exception ex)
                        {
                            DebugT.WriteLine($"⚠️ Error pooling CancellationTokenSource: {ex.Message}");
                            _cancellationTokenSource?.Dispose();
                        }
                        _cancellationTokenSource = null;
                    }

                    DebugT.WriteLine("✅ Memory-optimized resource cleanup completed with pooling");
                }
            });
        }

        public async Task<TranscriptionResult> TranscribeAsync(byte[] audioData)
        {
            var fastResult = await _azureService.TranscribeAsync(audioData);

            return new TranscriptionResult
            {
                Text = fastResult.Text,
                Confidence = fastResult.Confidence,
                ProcessingTime = fastResult.ProcessingTime,
                IsSuccess = fastResult.IsSuccess,
                ErrorMessage = fastResult.ErrorMessage,
                Timestamp = fastResult.Timestamp,
                ServiceUsed = ServiceName
            };
        }

        public async Task<bool> ValidateConnectionAsync()
        {
            return await _azureService.ValidateConnectionAsync();
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    DebugT.WriteLine("🧹 AzureTranscriptionServiceAdapter: Starting disposal...");

                    // Stop listening first
                    try
                    {
                        if (_isListening)
                        {
                            StopListeningAsync().Wait(TimeSpan.FromMilliseconds(500));
                        }
                    }
                    catch (Exception ex)
                    {
                        DebugT.WriteLine($"⚠️ Error stopping during disposal: {ex.Message}");
                    }

                    // Force cleanup of ALL resources during disposal (override pre-init protection)
                    var wasPreInitialized = _isPrepared;
                    _isPrepared = false; // Temporarily disable pre-init to allow full cleanup

                    try
                    {
                        CleanupAudioResourcesAsync().Wait(TimeSpan.FromMilliseconds(1000));
                    }
                    catch (Exception ex)
                    {
                        DebugT.WriteLine($"⚠️ Error in cleanup during disposal: {ex.Message}");
                    }

                    // CRITICAL: Ensure pre-initialized components are FULLY disposed for clean shutdown
                    if (wasPreInitialized)
                    {
                        lock (_lockObject)
                        {
                            try
                            {
                                // Clean up warm system components
                                if (_warmWaveIn != null)
                                {
                                    _warmWaveIn.DataAvailable -= OnMicrophoneDataAvailable;
                                    if (_warmMP3Buffer != null)
                                    {
                                        _warmWaveIn.DataAvailable -= _warmMP3Buffer.OnAudioDataAvailable;
                                    }
                                    _warmWaveIn.RecordingStopped -= OnRecordingStopped;
                                    _warmWaveIn.Dispose();
                                    _warmWaveIn = null;
                                }
                                if (_warmMP3Buffer != null)
                                {
                                    _warmMP3Buffer.Dispose();
                                    _warmMP3Buffer = null;
                                }
                                _isWarmSystemReady = false;

                                if (_waveIn != null)
                                {
                                    // Force unwire all events
                                    _waveIn.DataAvailable -= OnMicrophoneDataAvailable;
                                    if (_streamingMP3Buffer != null)
                                    {
                                        _waveIn.DataAvailable -= _streamingMP3Buffer.OnAudioDataAvailable;
                                    }
                                    _waveIn.RecordingStopped -= OnRecordingStopped;

                                    // Force stop and dispose
                                    try
                                    {
                                        _waveIn.StopRecording();
                                    }
                                    catch (Exception stopEx)
                                    {
                                        DebugT.WriteLine($"⚠️ Error stopping WaveIn: {stopEx.Message}");
                                    }
                                    _waveIn.Dispose();
                                    _waveIn = null;
                                }

                                _streamingMP3Buffer?.Dispose();
                                _streamingMP3Buffer = null;

                                DebugT.WriteLine("🧹 Final disposal: ALL pre-initialized components disposed for clean shutdown");
                            }
                            catch (Exception ex)
                            {
                                DebugT.WriteLine($"⚠️ Error in final pre-init cleanup: {ex.Message}");
                            }
                        }
                    }

                    // MEMORY OPTIMIZATION: Dispose pooled resources during final disposal
                    lock (_poolLock)
                    {
                        try
                        {
                            _pooledWaveIn?.Dispose();
                            _pooledWaveIn = null;

                            _pooledMP3Buffer?.Dispose();
                            _pooledMP3Buffer = null;

                            _pooledCancellationTokenSource?.Dispose();
                            _pooledCancellationTokenSource = null;

                            DebugT.WriteLine("🧹 Disposed all pooled resources during final cleanup");
                        }
                        catch (Exception ex)
                        {
                            DebugT.WriteLine($"⚠️ Error disposing pooled resources: {ex.Message}");
                        }
                    }

                    _azureService?.Dispose();
                    DebugT.WriteLine("✅ AzureTranscriptionServiceAdapter: Disposal completed");
                }
                _disposed = true;
            }
        }
    }
}
