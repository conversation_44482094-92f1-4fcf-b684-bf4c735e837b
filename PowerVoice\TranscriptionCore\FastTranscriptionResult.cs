namespace PowerVoice.TranscriptionCore
{
    /// <summary>
    /// Represents the result of a Fast Transcription API call
    /// </summary>
    public class FastTranscriptionResult
    {
        public string Text { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public List<WordInfo> Words { get; set; } = new();
        public int DurationMilliseconds { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Represents individual word information from transcription
    /// </summary>
    public class WordInfo
    {
        public string Text { get; set; } = string.Empty;
        public int OffsetMilliseconds { get; set; }
        public int DurationMilliseconds { get; set; }
        public double Confidence { get; set; }
    }

    /// <summary>
    /// Azure API response models
    /// </summary>
    public class AzureFastTranscriptionResponse
    {
        public int DurationMilliseconds { get; set; }
        public List<CombinedPhrase> CombinedPhrases { get; set; } = new();
        public List<Phrase> Phrases { get; set; } = new();
    }

    public class CombinedPhrase
    {
        public string Text { get; set; } = string.Empty;
    }

    public class Phrase
    {
        public int OffsetMilliseconds { get; set; }
        public int DurationMilliseconds { get; set; }
        public string Text { get; set; } = string.Empty;
        public List<Word> Words { get; set; } = new();
        public string Locale { get; set; } = string.Empty;
        public double Confidence { get; set; }
    }

    public class Word
    {
        public string Text { get; set; } = string.Empty;
        public int OffsetMilliseconds { get; set; }
        public int DurationMilliseconds { get; set; }
        public double Confidence { get; set; }
    }
}
