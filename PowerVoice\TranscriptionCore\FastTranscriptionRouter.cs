using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using PowerVoice.Extensions;
using PowerVoice.TranscriptionAzure;


namespace PowerVoice.TranscriptionCore
{
    /// <summary>
    /// Cloud Transcription Router - Delegates to Azure Speech Services, Whisper via OpenAI, or Gemini based on configuration
    /// Supports multiple providers: Azure Speech Services, Whisper via OpenAI API, Gemini (2.5-flash, 2.5-flash-lite)
    /// </summary>
    public class FastTranscriptionRouter : IDisposable
    {
        private readonly IConfiguration _configuration;
        private readonly IDisposable? _activeService;
        private readonly string _activeProvider;

        // Service instances
    private readonly AzureFastTranscriptionProvider? _azureService;

        // Constants for transcription modes
        public const string TRANSCRIPTION_MODE_FAST = TranscriptionModes.FAST;

        public FastTranscriptionRouter(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

            try
            {
                DebugT.WriteLine("🔍 UNIFIED FAST TRANSCRIPTION SERVICE INITIALIZATION");
                DebugT.WriteLine("════════════════════════════════════════════════════");

                // Read configuration flags

                DebugT.WriteLine($"   Configuration flags:");
                DebugT.WriteLine($"   - UseAzure: true");
                // Only Azure is supported in this build
                _activeProvider = "Azure Speech Services";
                DebugT.WriteLine("✅ Initializing Azure Speech Services");
                _azureService = new AzureFastTranscriptionProvider(_configuration);
                _activeService = _azureService;

                DebugT.WriteLine($"{DateTime.Now:HH:mm:ss:fff}\t🎯 Active transcription provider: {_activeProvider}");
                DebugT.WriteLine($"{DateTime.Now:HH:mm:ss:fff}\t════════════════════════════════════════════════════");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"{DateTime.Now:HH:mm:ss:fff}\t❌ Error initializing Unified Fast Transcription Service: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Transcribe audio using the configured provider (Azure, OpenAI, or Gemini)
        /// </summary>
        /// <param name="audioData">Audio data in supported format</param>
        /// <param name="contentType">Audio content type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Transcription result from the active provider</returns>
        public async Task<FastTranscriptionResult> TranscribeAsync(
            byte[] audioData,
            string contentType = "audio/wav",
            CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                DebugT.WriteLine("");
                DebugT.WriteLine("═══════════════════════════════════════════════════════════");
                DebugT.WriteLine($"🎤 UNIFIED FAST TRANSCRIPTION - {_activeProvider.ToUpper()}");
                DebugT.WriteLine($"   Audio size: {audioData.Length / 1024.0:F1} KB");
                DebugT.WriteLine($"   Content type: {contentType}");
                DebugT.WriteLine($"   Provider: {_activeProvider}");
                DebugT.WriteLine($"   Start time: {startTime:HH:mm:ss.fff}");
                DebugT.WriteLine("═══════════════════════════════════════════════════════════");

                FastTranscriptionResult result;

                // Route to the appropriate service
                if (_azureService != null)
                {
                    result = await _azureService.TranscribeAsync(audioData, contentType, cancellationToken);
                }
                else
                {
                    throw new InvalidOperationException("No transcription service is available");
                }

                var processingTime = DateTime.UtcNow - startTime;
                result.ProcessingTime = processingTime;

                DebugT.WriteLine("");
                DebugT.WriteLine($"✅ UNIFIED TRANSCRIPTION COMPLETED - {_activeProvider.ToUpper()}");
                DebugT.WriteLine($"   Total processing time: {processingTime.TotalMilliseconds:F0}ms");
                DebugT.WriteLine($"   Provider: {_activeProvider}");
                DebugT.WriteLine($"   Success: {result.IsSuccess}");
                if (result.IsSuccess)
                {
                    DebugT.WriteLine($"   Transcribed text: \"{result.Text}\"");
                    DebugT.WriteLine($"   Confidence: {result.Confidence:P1}");
                }
                else
                {
                    DebugT.WriteLine($"   Error: {result.ErrorMessage}");
                }
                DebugT.WriteLine("═══════════════════════════════════════════════════════════");

                return result;
            }
            catch (Exception ex)
            {
                var processingTime = DateTime.UtcNow - startTime;

                DebugT.WriteLine("");
                DebugT.WriteLine($"❌ UNIFIED TRANSCRIPTION FAILED - {_activeProvider.ToUpper()}");
                DebugT.WriteLine($"   Processing time: {processingTime.TotalMilliseconds:F0}ms");
                DebugT.WriteLine($"   Provider: {_activeProvider}");
                DebugT.WriteLine($"   Error: {ex.Message}");
                DebugT.WriteLine($"   Exception type: {ex.GetType().Name}");
                DebugT.WriteLine("═══════════════════════════════════════════════════════════");

                return new FastTranscriptionResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"{_activeProvider} transcription failed: {ex.Message}",
                    ProcessingTime = processingTime
                };
            }
        }

        /// <summary>
        /// Validate connection for the active provider
        /// </summary>
        public Task<bool> ValidateConnectionAsync()
        {
            try
            {
                DebugT.WriteLine($"{DateTime.Now:HH:mm:ss:fff}\t🔍 Validating {_activeProvider} connection...");

                bool isValid = false;


                if (_azureService != null)
                {
                    // Azure service doesn't have a ValidateConnectionAsync method, so we'll assume it's valid
                    isValid = true;
                    DebugT.WriteLine($"{DateTime.Now:HH:mm:ss:fff}\t✅ {_activeProvider} connection assumed valid (no validation method available)");
                }

                DebugT.WriteLine($"{DateTime.Now:HH:mm:ss:fff}\t{(isValid ? "✅" : "❌")} {_activeProvider} connection validation: {(isValid ? "SUCCESS" : "FAILED")}");
                return Task.FromResult(isValid);
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"{DateTime.Now:HH:mm:ss:fff}\t❌ {_activeProvider} connection validation failed: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Get the name of the active transcription provider
        /// </summary>
        public string GetActiveProvider() => _activeProvider;

        /// <summary>
        /// Get detailed provider information
        /// </summary>
        public string GetProviderDetails()
        {
            if (_azureService != null)
            {
                var region = _configuration["AzureSpeech:Region"] ?? "centralindia";
                return $"Azure Speech Services ({region})";
            }
            else
            {
                return "No provider available";
            }
        }

        public void Dispose()
        {
            try
            {
                _activeService?.Dispose();
                _azureService?.Dispose();
                DebugT.WriteLine($"🗑️ Unified Fast Transcription Service disposed ({_activeProvider})");
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ Error disposing Unified Fast Transcription Service: {ex.Message}");
            }
        }
    }
}