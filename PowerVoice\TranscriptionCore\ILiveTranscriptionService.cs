namespace PowerVoice.TranscriptionCore
{
    /// <summary>
    /// Interface for live transcription services that support real-time events
    /// This extends ITranscriptionService with live recording capabilities and events
    /// </summary>
    public interface ILiveTranscriptionService : ITranscriptionService
    {
        // Live recording methods
        Task StartListeningAsync();
        Task StopListeningAsync();
        Task PreInitializeAsync();

        // Events for real-time feedback
        event EventHandler<TranscriptionEventArgs>? TranscriptionReceived;
        event EventHandler<string>? ErrorOccurred;
        event EventHandler? SessionStarted;
        event EventHandler? SessionEnded;
        event EventHandler? SpeechStarted;
        event EventHandler? SpeechStopped;
        event EventHandler? VoiceDetected;
        event EventHandler? VoiceStopped;
    }
}
