namespace PowerVoice.TranscriptionCore
{
    /// <summary>
    /// Common interface for all transcription services
    /// </summary>
    public interface ITranscriptionService : IDisposable
    {
        /// <summary>
        /// Transcribe audio data to text
        /// </summary>
        /// <param name="audioData">Audio data in supported format</param>
        /// <returns>Transcription result</returns>
        Task<TranscriptionResult> TranscribeAsync(byte[] audioData);

        /// <summary>
        /// Test connection to the transcription service
        /// </summary>
        /// <returns>True if connection is valid</returns>
        Task<bool> ValidateConnectionAsync();

        /// <summary>
        /// Get the name of this transcription service
        /// </summary>
        string ServiceName { get; }
    }

    /// <summary>
    /// Common transcription result interface
    /// </summary>
    public class TranscriptionResult
    {
        public string Text { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string ServiceUsed { get; set; } = string.Empty;
    }

    /// <summary>
    /// Available transcription service types
    /// </summary>
    public enum TranscriptionServiceType
    {
        RealTime,
        FastTranscription,
        BatchTranscription
    }
}
