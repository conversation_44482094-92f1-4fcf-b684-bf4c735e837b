namespace PowerVoice.TranscriptionCore
{
    /// <summary>
    /// Event arguments for transcription events
    /// </summary>
    public class TranscriptionEventArgs : EventArgs
    {
        public string Text { get; }
        public TimeSpan? Latency { get; }

        public TranscriptionEventArgs(string text, TimeSpan? latency = null)
        {
            Text = text ?? throw new ArgumentNullException(nameof(text));
            Latency = latency;
        }
    }
}
