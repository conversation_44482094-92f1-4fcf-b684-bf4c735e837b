namespace PowerVoice.TranscriptionCore
{
    /// <summary>
    /// Constants for transcription modes
    /// </summary>
    public static class TranscriptionModes
    {
        /// <summary>
        /// Cloud transcription mode with multiple provider options (Azure Speech Services, Whisper via OpenAI, Gemini)
        /// </summary>
        public const string CLOUD = "CloudTranscription";

        /// <summary>
        /// Legacy constant for backward compatibility - maps to CLOUD
        /// </summary>
        public const string FAST = "FastTranscription";

        /// <summary>
        /// Local Whisper Tiny mode (tiny.en) - Fastest with decent accuracy
        /// </summary>
        public const string WHISPER_TINY = "WhisperTiny";

        /// <summary>
        /// Local Whisper Base mode (base.en) - Balanced speed and accuracy
        /// </summary>
        public const string WHISPER_BASE = "WhisperBase";

        /// <summary>
        /// Local Whisper Small mode (small.en) - High quality transcription
        /// </summary>
        public const string WHISPER_SMALL = "WhisperSmall";
    }

    /// <summary>
    /// Cloud transcription providers with clear naming
    /// </summary>
    public static class CloudTranscriptionProviders
    {
        /// <summary>
        /// Azure Speech Services (formerly "Azure Fast Transcription")
        /// </summary>
        public const string AZURE_SPEECH = "Azure";

        /// <summary>
        /// Whisper via OpenAI API (formerly "OpenAI Fast Transcription")
        /// </summary>
        public const string WHISPER_OPENAI = "OpenAI";

        /// <summary>
        /// Gemini 2.5 Flash model for audio transcription
        /// </summary>
        public const string GEMINI_FLASH = "Gemini-2.5-Flash";

        /// <summary>
        /// Gemini 2.5 Flash Lite model for audio transcription
        /// </summary>
        public const string GEMINI_FLASH_LITE = "Gemini-2.5-Flash-Lite";
    }

    /// <summary>
    /// Legacy provider constants for backward compatibility
    /// </summary>
    public static class FastTranscriptionProviders
    {
        public const string AZURE = CloudTranscriptionProviders.AZURE_SPEECH;
        public const string OPENAI = CloudTranscriptionProviders.WHISPER_OPENAI;
        public const string GEMINI_FLASH = CloudTranscriptionProviders.GEMINI_FLASH;
        public const string GEMINI_FLASH_LITE = CloudTranscriptionProviders.GEMINI_FLASH_LITE;
    }
}
