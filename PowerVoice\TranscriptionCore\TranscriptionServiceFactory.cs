using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NAudio.Wave;
using PowerVoice.AudioProcessing;
using PowerVoice.Extensions;
using PowerVoice.TranscriptionAzure;
using PowerVoice.TranscriptionWhisper;

namespace PowerVoice.TranscriptionCore
{
    /// <summary>
    /// Factory for creating transcription services based on configuration
    /// </summary>
    public class TranscriptionServiceFactory
    {
        private readonly IConfiguration _configuration;

        public TranscriptionServiceFactory(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        /// <summary>
        /// Create a transcription service based on current configuration
        /// </summary>
        /// <returns>Configured transcription service</returns>
        public ITranscriptionService CreateService()
        {
            string mode = _configuration["AzureSpeech:TranscriptionMode"] ?? TranscriptionModes.FAST;
            return CreateService(mode);
        }

        /// <summary>
        /// Create a specific transcription service
        /// </summary>
        /// <param name="serviceType">Type of service to create</param>
        /// <returns>Requested transcription service</returns>
        public ITranscriptionService CreateService(string serviceType)
        {
            try
            {
                DebugT.WriteLine($"🏭 Creating transcription service: {serviceType}");

                return serviceType switch
                {
                    TranscriptionModes.FAST =>
                        new AzureTranscriptionServiceAdapter(_configuration),

                    TranscriptionModes.WHISPER_TINY =>
                        new WhisperTinyTranscriptionService(_configuration),
                    TranscriptionModes.WHISPER_BASE =>
                        new WhisperBaseTranscriptionService(_configuration),
                    TranscriptionModes.WHISPER_SMALL =>
                        new WhisperSmallTranscriptionService(_configuration),

                    _ => throw new ArgumentException($"Unknown transcription service type: {serviceType}")
                };
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ Failed to create transcription service '{serviceType}': {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Create a live transcription service that supports real-time events based on configuration
        /// This is the PRIMARY factory method that chooses between different service types
        /// </summary>
        /// <returns>ILiveTranscriptionService implementation based on current configuration</returns>
        public ILiveTranscriptionService CreateLiveTranscriptionService()
        {
            string mode = _configuration["AzureSpeech:TranscriptionMode"] ?? TranscriptionModes.FAST;
            DebugT.WriteLine($"🏭 Creating live transcription service for mode: {mode}");

            return mode switch
            {
                TranscriptionModes.FAST =>
                    new AzureTranscriptionServiceAdapter(_configuration),
                TranscriptionModes.WHISPER_TINY =>
                new WhisperTinyTranscriptionService(_configuration),
                TranscriptionModes.WHISPER_BASE =>
                    new WhisperBaseTranscriptionService(_configuration),
                TranscriptionModes.WHISPER_SMALL =>
                    new WhisperSmallTranscriptionService(_configuration),

                _ => throw new ArgumentException($"Unknown transcription mode: {mode}")
            };
        }

        /// <summary>
        /// Get the current transcription mode from configuration
        /// </summary>
        /// <returns>Current mode or default</returns>
        public string GetCurrentMode()
        {
            return _configuration["AzureSpeech:TranscriptionMode"] ?? TranscriptionModes.FAST;
        }

        /// <summary>
        /// Update the transcription mode in configuration (for settings changes)
        /// </summary>
        /// <param name="mode">New transcription mode</param>
        public void UpdateMode(string mode)
        {
            if (!IsServiceTypeSupported(mode))
            {
                throw new ArgumentException($"Unsupported transcription mode: {mode}");
            }

            // Update in-memory configuration
            _configuration["AzureSpeech:TranscriptionMode"] = mode;
            DebugT.WriteLine($"🔄 Transcription mode updated to: {mode}");
        }

        /// <summary>
        /// Get available transcription service types
        /// </summary>
        /// <returns>Array of available service types</returns>
        public static string[] GetAvailableServiceTypes()
        {
            return new[]
            {
                TranscriptionModes.FAST,
                TranscriptionModes.WHISPER_TINY,
                TranscriptionModes.WHISPER_BASE,
                TranscriptionModes.WHISPER_SMALL
            };
        }

        /// <summary>
        /// Validate that a service type is supported
        /// </summary>
        /// <param name="serviceType">Service type to validate</param>
        /// <returns>True if the service type is supported</returns>
        public static bool IsServiceTypeSupported(string serviceType)
        {
            return GetAvailableServiceTypes().Contains(serviceType);
        }
    }
}
