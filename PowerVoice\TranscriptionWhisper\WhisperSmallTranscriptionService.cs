using Microsoft.Extensions.Configuration;

namespace PowerVoice.TranscriptionWhisper
{
    /// <summary>
    /// Whisper Small transcription service - highest accuracy for the size.
    /// Uses ggml-small.en model (~465MB) for excellent transcription quality.
    /// </summary>
    public class WhisperSmallTranscriptionService : WhisperTranscriptionServiceBase
    {
        public override string ServiceName => "Whisper Small";
        protected override string ModelFileName => "ggml-small.en.bin";


        public WhisperSmallTranscriptionService(IConfiguration configuration)
            : base(configuration)
        {
        }
    }
}
