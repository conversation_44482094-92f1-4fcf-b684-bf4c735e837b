using Microsoft.Extensions.Configuration;

namespace PowerVoice.TranscriptionWhisper
{
    /// <summary>
    /// Whisper Tiny transcription service - fastest, smallest model with good accuracy.
    /// Uses ggml-tiny.en model (~39MB) for real-time transcription.
    /// </summary>
    public class WhisperTinyTranscriptionService : WhisperTranscriptionServiceBase
    {
        public override string ServiceName => "Whisper Tiny";
        protected override string ModelFileName => "ggml-tiny.en.bin";


        public WhisperTinyTranscriptionService(IConfiguration configuration)
            : base(configuration)
        {
        }
    }
}
