using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading;
using Microsoft.Extensions.Configuration;
using NAudio.Wave;
using PowerVoice.AudioProcessing;
using PowerVoice.Extensions;
using PowerVoice.TranscriptionCore;
using Whisper.net;
using Whisper.net.Ggml;

namespace PowerVoice.TranscriptionWhisper
{
    /// <summary>
    /// Base class for Whisper transcription services with shared functionality.
    /// Supports different model sizes: tiny, base, small with maximum code reuse.
    /// </summary>
    public abstract class WhisperTranscriptionServiceBase : ILiveTranscriptionService
    {
        protected readonly IConfiguration _configuration;
        protected readonly object _lock = new object();

        private WaveInEvent? _waveIn;
        private StreamingAudioBuffer? _streamingPcmBuffer;
        private RealTimeSilenceFilter? _silenceFilter;
        private bool _isListening;
        private bool _disposed;

        // WARM AUDIO SYSTEM: Keep audio components initialized but NOT recording for instant start
        private WaveInEvent? _warmWaveIn;
        private StreamingAudioBuffer? _warmStreamingPcmBuffer;
        private RealTimeSilenceFilter? _warmSilenceFilter;
        private bool _isWarmSystemReady = false;

        private WhisperFactory? _whisperFactory;
        private readonly SemaphoreSlim _transcribeGate = new SemaphoreSlim(1, 1);
        private Timer? _whisperDisposeTimer;
        private readonly object _whisperFactoryLock = new object();
        private DateTime _lastWhisperUsage;
        private DateTime _recordingStartTime; // Track recording session duration

        private readonly WaveFormat _waveFormat = new WaveFormat(16000, 16, 1);
        private const double VOICE_THRESHOLD = 200.0; // Lowered from 500.0 to be more sensitive to speech onset

        // Static flag to ensure process optimization is applied only once per application lifetime
        private static bool _processOptimizationApplied = false;
        private static readonly object _processOptimizationLock = new object();

        // Enhanced debugging constants (borrowed from Azure pattern)
        private const string DEBUG_SEPARATOR = "═══════════════════════════════════════════════════════════";

        public event EventHandler<TranscriptionEventArgs>? TranscriptionReceived;
        public event EventHandler<string>? ErrorOccurred;
        public event EventHandler? SessionStarted;
        public event EventHandler? SessionEnded;
        public event EventHandler? SpeechStarted;
        public event EventHandler? SpeechStopped;
        public event EventHandler? VoiceDetected;
        public event EventHandler? VoiceStopped;

        // Abstract properties that derived classes must implement
        public abstract string ServiceName { get; }
        protected abstract string ModelFileName { get; }


        protected WhisperTranscriptionServiceBase(IConfiguration configuration)
        {
            _configuration = configuration;
            DebugT.WriteLine($"🔧 {ServiceName} initialized successfully");
        }

        public async Task PreInitializeAsync()
        {
            var initStart = DateTime.UtcNow;
            DebugT.WriteLine($"🔄 Starting {ServiceName} pre-initialization...");

            try
            {
                await Task.Run(() =>
                {
                    // Enhanced resource initialization (borrowed from Azure pattern)
                    lock (_lock)
                    {
                        if (_waveIn == null)
                        {
                            DebugT.WriteLine("   Initializing audio capture...");
                            _waveIn = new WaveInEvent
                            {
                                WaveFormat = _waveFormat,
                                BufferMilliseconds = 50,
                                NumberOfBuffers = 2
                            };
                            _waveIn.DataAvailable += OnWaveDataAvailable;
                            _waveIn.RecordingStopped += (_, __) => DebugT.WriteLine($"🛑 {ServiceName}: Recording stopped");

                            DebugT.WriteLine("   Initializing audio filters and buffers...");
                            // More sensitive VAD settings to prevent cutting off first words:
                            // - Threshold: 200.0 (was 500.0) - more sensitive to speech
                            // - Max silence: 800ms (was 1000ms) - shorter silence retention
                            // - Sentence boundary: 1200ms (was 1500ms) - faster sentence detection
                            _silenceFilter = new RealTimeSilenceFilter(VOICE_THRESHOLD, 800, _waveFormat.SampleRate, 1200);
                            _streamingPcmBuffer = new StreamingAudioBuffer(_waveFormat, VOICE_THRESHOLD, 800);
                            _waveIn.DataAvailable += _streamingPcmBuffer.OnAudioDataAvailable;
                        }

                        // 🚀 WARM AUDIO SYSTEM - Create pre-initialized audio devices for instant Alt-key response
                        DebugT.WriteLine("   Creating warm audio system for instant startup...");
                        _warmWaveIn = new WaveInEvent
                        {
                            WaveFormat = _waveFormat,
                            BufferMilliseconds = 10, // 10ms for ultra-low latency
                            NumberOfBuffers = 2
                        };
                        _warmStreamingPcmBuffer = new StreamingAudioBuffer(_waveFormat, VOICE_THRESHOLD, 800);
                        _warmSilenceFilter = new RealTimeSilenceFilter(VOICE_THRESHOLD, 800, _waveFormat.SampleRate, 1200);
                        _isWarmSystemReady = true;
                        DebugT.WriteLine("   ✅ Warm audio system ready (devices initialized but NOT recording)");
                    }
                });

                // ⚡ PRE-LOAD WHISPER MODEL NOW to eliminate Alt-key delay
                DebugT.WriteLine("   Pre-loading Whisper model to eliminate startup delay...");
                await EnsureWhisperModelAsync();

                var initTime = DateTime.UtcNow - initStart;
                DebugT.WriteLine($"✅ {ServiceName} pre-initialization completed successfully in {initTime.TotalMilliseconds:F0}ms");
            }
            catch (Exception ex)
            {
                var initTime = DateTime.UtcNow - initStart;
                DebugT.WriteLine($"❌ {ServiceName} pre-initialization failed after {initTime.TotalMilliseconds:F0}ms: {ex.Message}");
                throw;
            }
        }

        public async Task StartListeningAsync()
        {
            if (_isListening) return;

            var startTime = DateTime.UtcNow;
            DebugT.WriteLine($"🔄 Starting {ServiceName} listening session...");

            try
            {
                // 🚀 WARM SYSTEM: Use pre-initialized warm audio devices for instant startup
                lock (_lock)
                {
                    if (_isWarmSystemReady && _warmWaveIn != null)
                    {
                        DebugT.WriteLine($"⚡ {ServiceName}: Using warm audio system for instant startup!");

                        // Promote warm system to active system
                        _waveIn?.Dispose();
                        _waveIn = _warmWaveIn;
                        _streamingPcmBuffer = _warmStreamingPcmBuffer!;
                        _silenceFilter = _warmSilenceFilter!;

                        // Set up event handlers for the warm system
                        _waveIn.DataAvailable += OnWaveDataAvailable;
                        _waveIn.DataAvailable += _streamingPcmBuffer.OnAudioDataAvailable;
                        _waveIn.RecordingStopped += (_, __) => DebugT.WriteLine($"🛑 {ServiceName}: Recording stopped");

                        // Start recording immediately with warm system
                        _waveIn.StartRecording();
                        _isListening = true;
                        _recordingStartTime = DateTime.UtcNow;

                        // Mark warm system as used
                        _warmWaveIn = null;
                        _warmStreamingPcmBuffer = null;
                        _warmSilenceFilter = null;
                        _isWarmSystemReady = false;

                        // Recreate warm system for next use (async to not block)
                        _ = Task.Run(RecreateWarmSystemAsync);
                    }
                    else
                    {
                        // Fallback to cold start if warm system not ready
                        DebugT.WriteLine($"❄️ {ServiceName}: Warm system not ready, using cold start");
                        _waveIn?.StartRecording();
                        _isListening = true;
                        _recordingStartTime = DateTime.UtcNow;
                    }
                }

                DebugT.WriteLine($"🎤 {ServiceName} recording started in {(DateTime.UtcNow - startTime).TotalMilliseconds:F0}ms");
                SessionStarted?.Invoke(this, EventArgs.Empty);

                // Load Whisper model in parallel while we're already recording
                await EnsureWhisperModelAsync();

                var sessionTime = DateTime.UtcNow - startTime;
                DebugT.WriteLine($"✅ {ServiceName} listening session fully ready in {sessionTime.TotalMilliseconds:F0}ms");
            }
            catch (Exception ex)
            {
                var sessionTime = DateTime.UtcNow - startTime;
                DebugT.WriteLine($"❌ Failed to start {ServiceName} listening after {sessionTime.TotalMilliseconds:F0}ms: {ex.Message}");
                ErrorOccurred?.Invoke(this, $"Failed to start {ServiceName}: {ex.Message}");
                throw;
            }
        }

        public async Task StopListeningAsync()
        {
            if (!_isListening) return;

            var stopTime = DateTime.UtcNow;
            DebugT.WriteLine($"🔄 Stopping {ServiceName} listening session...");

            try
            {
                lock (_lock)
                {
                    _waveIn?.StopRecording();
                    _isListening = false;
                }

                // Process any remaining audio as a final chunk so text is delivered on Alt release
                try
                {
                    byte[]? finalAudio = null;
                    lock (_lock)
                    {
                        if (_streamingPcmBuffer != null)
                        {
                            finalAudio = _streamingPcmBuffer.GetFilteredAudio();
                            // Reset for the next session
                            _streamingPcmBuffer.Reset();
                        }
                    }

                    if (finalAudio != null && finalAudio.Length > 0)
                    {
                        var chunkStart = DateTime.UtcNow;
                        var result = await TranscribeAsync(finalAudio);
                        if (result.IsSuccess && !string.IsNullOrWhiteSpace(result.Text))
                        {
                            var latency = DateTime.UtcNow - chunkStart;
                            DebugT.WriteLine($"🏁 {ServiceName} final chunk transcribed: '{result.Text}' in {latency.TotalMilliseconds:F0}ms");
                            TranscriptionReceived?.Invoke(this, new TranscriptionEventArgs(result.Text, latency));
                        }
                    }
                    else
                    {
                        DebugT.WriteLine($"ℹ️ {ServiceName}: No remaining audio to transcribe on stop");
                    }
                }
                catch (Exception ex)
                {
                    DebugT.WriteLine($"⚠️ {ServiceName} finalization error: {ex.Message}");
                    ErrorOccurred?.Invoke(this, $"{ServiceName} finalization error: {ex.Message}");
                }

                var sessionTime = DateTime.UtcNow - stopTime;
                DebugT.WriteLine($"✅ {ServiceName} listening session stopped successfully in {sessionTime.TotalMilliseconds:F0}ms");
                SessionEnded?.Invoke(this, EventArgs.Empty);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                var sessionTime = DateTime.UtcNow - stopTime;
                DebugT.WriteLine($"❌ Failed to stop {ServiceName} after {sessionTime.TotalMilliseconds:F0}ms: {ex.Message}");
                ErrorOccurred?.Invoke(this, $"Failed to stop {ServiceName}: {ex.Message}");
            }
        }

        public async Task<TranscriptionResult> TranscribeAsync(byte[] audioData)
        {
            // On-demand transcription call for compatibility; use the processor to transcribe the provided WAV/PCM
            var startTime = DateTime.UtcNow;

            try
            {
                await EnsureWhisperModelAsync();
                await _transcribeGate.WaitAsync();
                try
                {
                    using var ms = new MemoryStream(audioData);
                    var transcriptionStart = DateTime.UtcNow;
                    var sb = new StringBuilder();

                    if (_whisperFactory == null)
                    {
                        throw new InvalidOperationException($"{ServiceName} factory is not initialized");
                    }

                    var language = _configuration["Whisper:AudioTranscription:Language"] ?? "en";
                    var temperatureStr = _configuration["Whisper:AudioTranscription:Temperature"] ?? "0.0";
                    double.TryParse(temperatureStr, out var temperature);

                    // Enhanced debug output with better formatting (borrowed from Azure pattern)
                    DebugT.WriteLine("");
                    DebugT.WriteLine(DEBUG_SEPARATOR);
                    DebugT.WriteLine($"🎤 STARTING {ServiceName.ToUpper()} TRANSCRIPTION");
                    DebugT.WriteLine($"   Model: {ModelFileName}");
                    DebugT.WriteLine($"   Audio size: {audioData.Length / 1024.0:F1} KB");
                    DebugT.WriteLine($"   Language: {language}");
                    DebugT.WriteLine($"   Temperature: {temperature:F1}");
                    DebugT.WriteLine($"   Start time: {startTime:HH:mm:ss.fff}");
                    DebugT.WriteLine(DEBUG_SEPARATOR);

                    using var processor = _whisperFactory.CreateBuilder()
                        .WithLanguage(language)
                        .WithTemperature((float)temperature)
                        .WithSingleSegment()
                        .WithPrompt("This is a transcript with proper punctuation and capitalization.")
                        .WithThreads(Environment.ProcessorCount / 2)

                        .WithGreedySamplingStrategy()
                        .ParentBuilder
                        .Build();
                    var counter = 1;

                    await foreach (var segment in processor.ProcessAsync(ms))
                    {
                        // Add condition to treat [BLANK_AUDIO] as empty test
                        if (segment.Text == "[BLANK_AUDIO]" || string.IsNullOrWhiteSpace(segment.Text))
                        {
                            DebugT.WriteLine($"ℹ️ Empty {ServiceName} segment");
                        }
                        else
                        {
                            sb.Append(segment.Text);
                            //add debugt log with segment text and segment number
                            DebugT.WriteLine($"📝 {ServiceName} segment {counter++} transcribed: '{segment.Text}'");
                        }
                    }

                    var transcriptionTime = DateTime.UtcNow - transcriptionStart;
                    var totalProcessingTime = DateTime.UtcNow - startTime;
                    var text = sb.ToString().Trim();

                    // CRITICAL: Ensure [BLANK_AUDIO] never appears in output, even if it slipped through
                    if (text.Contains("[BLANK_AUDIO]"))
                    {
                        text = text.Replace("[BLANK_AUDIO]", "").Trim();
                        DebugT.WriteLine("🧹 Removed [BLANK_AUDIO] from final transcription text");
                    }

                    // Enhanced success output with detailed metrics (borrowed from Azure pattern)
                    DebugT.WriteLine("");
                    DebugT.WriteLine($"✅ {ServiceName.ToUpper()} TRANSCRIPTION COMPLETED SUCCESSFULLY");
                    DebugT.WriteLine($"   Transcription time: {transcriptionTime.TotalMilliseconds:F0}ms");
                    DebugT.WriteLine($"   Total processing time: {totalProcessingTime.TotalMilliseconds:F0}ms");
                    DebugT.WriteLine($"   Transcribed text: \"{text}\"");
                    DebugT.WriteLine($"   Text length: {text.Length} characters");
                    DebugT.WriteLine($"   Audio-to-text ratio: {(audioData.Length / Math.Max(text.Length, 1)):F1} bytes/char");
                    DebugT.WriteLine(DEBUG_SEPARATOR);

                    var result = new TranscriptionResult
                    {
                        Text = text,
                        Confidence = 0.85,
                        ProcessingTime = totalProcessingTime,
                        IsSuccess = true,
                        ServiceUsed = ServiceName
                    };
                    return result;
                }
                finally
                {
                    _transcribeGate.Release();
                }
            }
            catch (Exception ex)
            {
                var processingTime = DateTime.UtcNow - startTime;

                // Enhanced error output with detailed metrics (borrowed from Azure pattern)
                DebugT.WriteLine("");
                DebugT.WriteLine($"❌ {ServiceName.ToUpper()} TRANSCRIPTION FAILED");
                DebugT.WriteLine($"   Processing time: {processingTime.TotalMilliseconds:F0}ms");
                DebugT.WriteLine($"   Error: {ex.Message}");
                DebugT.WriteLine($"   Exception type: {ex.GetType().Name}");
                DebugT.WriteLine($"   Audio size: {audioData?.Length ?? 0} bytes");
                DebugT.WriteLine(DEBUG_SEPARATOR);

                return new TranscriptionResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    ProcessingTime = processingTime,
                    ServiceUsed = ServiceName
                };
            }
        }

        public async Task<bool> ValidateConnectionAsync()
        {
            try
            {
                await EnsureWhisperModelAsync();
                return true;
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"⚠️ {ServiceName} validation failed: {ex.Message}");
                return false;
            }
        }

        private Task EnsureWhisperModelAsync()
        {
            lock (_whisperFactoryLock)
            {
                var modelPath = ResolveModelPath(ModelFileName);

                if (!File.Exists(modelPath))
                {
                    // No runtime downloads allowed: surface a clear error
                    DebugT.WriteLine($"❌ {ServiceName} model not found at: {modelPath}. Place the {ModelFileName} file in this path.");
                    throw new FileNotFoundException($"{ServiceName} model not found. Expected at: {modelPath}", modelPath);
                }

                // Initialize factory from path once; avoid disposing while in-flight transcriptions may be running
                if (_whisperFactory == null)
                {
                    var loadingStart = DateTime.UtcNow;
                    DebugT.WriteLine($"🔄 Loading {ServiceName} model: {ModelFileName} from {modelPath}");
                    DebugT.WriteLine($"   Model file size: {new FileInfo(modelPath).Length / (1024.0 * 1024.0):F1} MB");
                    _whisperFactory = WhisperFactory.FromPath(modelPath);

                    var loadingTime = DateTime.UtcNow - loadingStart;
                    DebugT.WriteLine($"✅ {ServiceName} model loaded successfully in {loadingTime.TotalMilliseconds:F0}ms");
                    DebugT.WriteLine($"   Loading rate: {(new FileInfo(modelPath).Length / (1024.0 * 1024.0)) / Math.Max(loadingTime.TotalSeconds, 0.1):F1} MB/sec");

                    // Apply process optimization only once per application lifetime
                    ApplyProcessOptimizationOnce();
                }

                // Update last usage time and reset the disposal timer
                _lastWhisperUsage = DateTime.UtcNow;
                _whisperDisposeTimer?.Dispose();
                _whisperDisposeTimer = new Timer(DisposeWhisperFactoryOnTimeout, null, (int)TimeSpan.FromMinutes(2).TotalMilliseconds, Timeout.Infinite);
                DebugT.WriteLine($"🕐 {ServiceName} disposal timer set for 2 minutes from now");
            }

            return Task.CompletedTask;
        }

        // No downloader usage; model name is used only to resolve local file path
        private static string ResolveModelPath(string modelFileName)
        {
            // Store under ModelAssets (matching the build output structure)
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;
            var dir = Path.Combine(baseDir, "ModelAssets");
            return Path.Combine(dir, modelFileName);
        }

        private static void ApplyProcessOptimizationOnce()
        {
            lock (_processOptimizationLock)
            {
                if (!_processOptimizationApplied)
                {
                    try
                    {
                        var proc = Process.GetCurrentProcess();
                        proc.ProcessorAffinity = (IntPtr)0x0F;  // cores 0-3
                        proc.PriorityClass = ProcessPriorityClass.High;
                        _processOptimizationApplied = true;
                        DebugT.WriteLine($"🚀 Process optimization applied once: High priority + 4 cores (will persist across factory recreations)");
                    }
                    catch (Exception ex)
                    {
                        DebugT.WriteLine($"⚠️ Failed to apply process optimization: {ex.Message}");
                    }
                }
                else
                {
                    DebugT.WriteLine($"ℹ️ Process optimization already applied - skipping (factory recreation)");
                }
            }
        }

        private void DisposeWhisperFactoryOnTimeout(object? state)
        {
            lock (_whisperFactoryLock)
            {
                if (_whisperFactory != null)
                {
                    // Check if we're still within the listening session or actively transcribing
                    bool isTranscribing = _transcribeGate.CurrentCount == 0;

                    if (_isListening || isTranscribing)
                    {
                        // If still listening or transcribing, reschedule disposal for later
                        string reason = _isListening ? "listening" : "transcribing";
                        DebugT.WriteLine($"🕐 {ServiceName} still {reason}, postponing factory disposal for another 2 minutes");
                        _whisperDisposeTimer?.Dispose();
                        _whisperDisposeTimer = new Timer(DisposeWhisperFactoryOnTimeout, null, (int)TimeSpan.FromMinutes(2).TotalMilliseconds, Timeout.Infinite);
                        return;
                    }

                    var timeSinceLastUsage = DateTime.UtcNow - _lastWhisperUsage;
                    if (timeSinceLastUsage >= TimeSpan.FromMinutes(2))
                    {
                        DebugT.WriteLine($"🧹 Disposing {ServiceName} factory after {timeSinceLastUsage.TotalMinutes:F1} minutes of inactivity");
                        try
                        {
                            _whisperFactory.Dispose();
                            _whisperFactory = null;
                            DebugT.WriteLine($"✅ {ServiceName} factory disposed successfully to free memory");
                        }
                        catch (Exception ex)
                        {
                            DebugT.WriteLine($"⚠️ Error disposing {ServiceName} factory: {ex.Message}");
                        }
                        finally
                        {
                            _whisperDisposeTimer?.Dispose();
                            _whisperDisposeTimer = null;
                        }
                    }
                    else
                    {
                        // Reschedule for the remaining time
                        var remainingTime = TimeSpan.FromMinutes(2) - timeSinceLastUsage;
                        DebugT.WriteLine($"🕐 {ServiceName} rescheduling disposal for {remainingTime.TotalSeconds:F0} more seconds");
                        _whisperDisposeTimer?.Dispose();
                        _whisperDisposeTimer = new Timer(DisposeWhisperFactoryOnTimeout, null, (int)remainingTime.TotalMilliseconds, Timeout.Infinite);
                    }
                }
            }
        }

        // Processor is created per transcription call to avoid holding onto unmanaged resources
        private void OnWaveDataAvailable(object? sender, WaveInEventArgs e)
        {
            if (!_isListening || e.BytesRecorded == 0) return;

            try
            {
                // Basic voice activity for UI
                SimpleVad(e.Buffer, e.BytesRecorded);

                // Update sentence boundary detection
                _silenceFilter?.ProcessAudioChunk(e.Buffer, e.BytesRecorded);

                // Chunk at sentence boundaries to keep latency low
                if (_silenceFilter != null && _silenceFilter.IsAtSentenceBoundary() && _streamingPcmBuffer != null)
                {
                    var wavBytes = _streamingPcmBuffer.GetFilteredAudio();
                    if (wavBytes.Length > 0)
                    {
                        // Reset buffer for next chunk
                        _streamingPcmBuffer.Reset();

                        var chunkStart = DateTime.UtcNow;
                        _ = Task.Run(async () =>
                        {
                            var result = await TranscribeAsync(wavBytes);
                            if (result.IsSuccess)
                            {
                                var latency = DateTime.UtcNow - chunkStart;
                                DebugT.WriteLine($"🎯 {ServiceName} chunk processed successfully in {latency.TotalMilliseconds:F0}ms: '{result.Text}' (chunk size: {wavBytes.Length / 1024.0:F1} KB)");
                                TranscriptionReceived?.Invoke(this, new TranscriptionEventArgs(result.Text, latency));
                            }
                            else if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
                            {
                                var latency = DateTime.UtcNow - chunkStart;
                                DebugT.WriteLine($"⚠️ {ServiceName} chunk processing failed after {latency.TotalMilliseconds:F0}ms: {result.ErrorMessage}");
                                ErrorOccurred?.Invoke(this, result.ErrorMessage);
                            }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"{ServiceName} audio error: {ex.Message}");
            }
        }

        private bool _speaking;
        private void SimpleVad(byte[] buffer, int bytes)
        {
            try
            {
                long total = 0; int count = 0;
                for (int i = 0; i + 1 < bytes; i += 8)
                {
                    // Read 16-bit little-endian PCM sample safely
                    short sample = BitConverter.ToInt16(buffer, i);
                    // Avoid overflow for short.MinValue when taking absolute value
                    int s = sample;
                    int abs = (s == short.MinValue) ? 32768 : Math.Abs(s);
                    total += abs;
                    count++;
                }
                var avg = count > 0 ? (double)total / count : 0;
                bool speaking = avg > (VOICE_THRESHOLD / 10) && avg < 50000;
                if (speaking && !_speaking)
                {
                    _speaking = true;
                    VoiceDetected?.Invoke(this, EventArgs.Empty);
                    SpeechStarted?.Invoke(this, EventArgs.Empty);
                }
                else if (!speaking && _speaking)
                {
                    _speaking = false;
                    VoiceStopped?.Invoke(this, EventArgs.Empty);
                    SpeechStopped?.Invoke(this, EventArgs.Empty);
                }
            }
            catch
            {
                if (_speaking)
                {
                    _speaking = false;
                    VoiceStopped?.Invoke(this, EventArgs.Empty);
                    SpeechStopped?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            var disposeStart = DateTime.UtcNow;
            DebugT.WriteLine($"🔄 Starting {ServiceName} disposal...");

            try
            {
                _disposed = true;

                // Enhanced disposal pattern (borrowed from Azure pattern)
                // Stop any ongoing operations first
                if (_isListening)
                {
                    DebugT.WriteLine("   Stopping active listening session...");
                    _ = StopListeningAsync(); // Fire and forget for disposal
                }

                // Wait for any pending transcriptions to complete (with timeout)
                var waitStart = DateTime.UtcNow;
                var timeout = TimeSpan.FromSeconds(10); // Reasonable timeout for local processing
                while (!_transcribeGate.Wait(100) && (DateTime.UtcNow - waitStart) < timeout)
                {
                    DebugT.WriteLine("   Waiting for pending transcriptions to complete...");
                }

                // Dispose resources in order of dependency
                try
                {
                    DebugT.WriteLine("   Disposing Whisper disposal timer...");
                    _whisperDisposeTimer?.Dispose();
                }
                catch (Exception ex)
                {
                    DebugT.WriteLine($"⚠️ Warning disposing Whisper timer: {ex.Message}");
                }

                try
                {
                    DebugT.WriteLine("   Disposing Whisper factory...");
                    lock (_whisperFactoryLock)
                    {
                        _whisperFactory?.Dispose();
                        _whisperFactory = null;
                    }
                }
                catch (Exception ex)
                {
                    DebugT.WriteLine($"⚠️ Warning disposing Whisper factory: {ex.Message}");
                }

                try
                {
                    DebugT.WriteLine("   Disposing audio input...");
                    _waveIn?.Dispose();
                }
                catch (Exception ex)
                {
                    DebugT.WriteLine($"⚠️ Warning disposing wave input: {ex.Message}");
                }

                try
                {
                    DebugT.WriteLine("   Disposing audio buffer...");
                    _streamingPcmBuffer?.Dispose();
                }
                catch (Exception ex)
                {
                    DebugT.WriteLine($"⚠️ Warning disposing audio buffer: {ex.Message}");
                }

                try
                {
                    DebugT.WriteLine("   Disposing audio buffer...");
                    _warmStreamingPcmBuffer?.Dispose();
                }
                catch (Exception ex)
                {
                    DebugT.WriteLine($"⚠️ Warning disposing warm audio buffer: {ex.Message}");
                }

                try
                {
                    DebugT.WriteLine("   Disposing transcription gate...");
                    _transcribeGate.Dispose();
                }
                catch (Exception ex)
                {
                    DebugT.WriteLine($"⚠️ Warning disposing transcription gate: {ex.Message}");
                }

                var disposalTime = DateTime.UtcNow - disposeStart;
                DebugT.WriteLine($"✅ {ServiceName} disposed successfully in {disposalTime.TotalMilliseconds:F0}ms");
            }
            catch (Exception ex)
            {
                var disposalTime = DateTime.UtcNow - disposeStart;
                DebugT.WriteLine($"❌ Error during {ServiceName} disposal after {disposalTime.TotalMilliseconds:F0}ms: {ex.Message}");
            }
        }

        /// <summary>
        /// Recreates the warm audio system for next use after it has been consumed
        /// </summary>
        private async Task RecreateWarmSystemAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    lock (_lock)
                    {
                        if (!_isWarmSystemReady)
                        {
                            DebugT.WriteLine($"🔄 {ServiceName}: Recreating warm audio system...");

                            _warmWaveIn = new WaveInEvent
                            {
                                WaveFormat = _waveFormat,
                                BufferMilliseconds = 10, // 10ms for ultra-low latency
                                NumberOfBuffers = 2
                            };
                            _warmStreamingPcmBuffer = new StreamingAudioBuffer(_waveFormat, VOICE_THRESHOLD, 800);
                            _warmSilenceFilter = new RealTimeSilenceFilter(VOICE_THRESHOLD, 800, _waveFormat.SampleRate, 1200);
                            _isWarmSystemReady = true;

                            DebugT.WriteLine($"✅ {ServiceName}: Warm audio system recreated and ready");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                DebugT.WriteLine($"❌ {ServiceName}: Failed to recreate warm system: {ex.Message}");
            }
        }
    }
}
