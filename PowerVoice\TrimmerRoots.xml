<?xml version="1.0" encoding="utf-8"?>
<!--
ULTRA-AGGRESSIVE TRIMMING CONFIGURATION for Sub-10MB Target
Specifies exactly which .NET components to preserve, eliminating everything else
This will aggressively remove unused framework components
-->
<linker>
  <!-- PRESERVE ONLY ESSENTIAL APPLICATION ASSEMBLIES -->
  <assembly fullname="PowerVoice" preserve="all" />

  <!-- AUDIO PROCESSING: Keep only NAudio essentials -->
  <assembly fullname="NAudio.Core">
    <type fullname="NAudio.Wave.WaveInEvent" preserve="all" />
    <type fullname="NAudio.Wave.WaveFormat" preserve="all" />
    <type fullname="NAudio.Wave.WaveInEventArgs" preserve="all" />
    <type fullname="NAudio.Wave.StoppedEventArgs" preserve="all" />
    <type fullname="NAudio.Wave.WaveFileWriter" preserve="all" />
  </assembly>

  <assembly fullname="NAudio.WinMM">
    <type fullname="NAudio.Wave.WaveIn" preserve="all" />
  </assembly>

  <!-- WPF ULTRA-MINIMAL: Only essential WPF components -->
  <assembly fullname="PresentationFramework">
    <type fullname="System.Windows.Application" />
    <type fullname="System.Windows.Window" />
    <type fullname="System.Windows.Controls.Button" />
    <type fullname="System.Windows.Controls.TextBox" />
    <type fullname="System.Windows.Controls.Label" />
    <type fullname="System.Windows.Controls.Grid" />
    <type fullname="System.Windows.Controls.StackPanel" />
    <type fullname="System.Windows.Markup.XamlReader" />
  </assembly>

  <assembly fullname="PresentationCore">
    <!-- EXCLUDE GRAPHICS ACCELERATION -->
    <type fullname="System.Windows.Media.RenderOptions" />
    <type fullname="System.Windows.Interop.RenderMode" />
    <!-- Exclude: Graphics, Visual3D, all GPU acceleration types -->
  </assembly>

  <assembly fullname="WindowsBase">
    <type fullname="System.Windows.Threading.Dispatcher" />
    <type fullname="System.Windows.Threading.DispatcherObject" />
  </assembly>

  <!-- HTTP CLIENT: For Azure/OpenAI API calls -->
  <assembly fullname="System.Net.Http">
    <type fullname="System.Net.Http.HttpClient" />
    <type fullname="System.Net.Http.HttpContent" />
    <type fullname="System.Net.Http.StringContent" />
    <type fullname="System.Net.Http.HttpResponseMessage" />
  </assembly>

  <!-- JSON SERIALIZATION: For API calls -->
  <assembly fullname="System.Text.Json">
    <type fullname="System.Text.Json.JsonSerializer" />
    <type fullname="System.Text.Json.JsonDocument" />
    <type fullname="System.Text.Json.JsonElement" />
  </assembly>

  <!-- CONFIGURATION: For appsettings.json -->
  <assembly fullname="Microsoft.Extensions.Configuration">
    <type fullname="Microsoft.Extensions.Configuration.IConfiguration" />
    <type fullname="Microsoft.Extensions.Configuration.ConfigurationBuilder" />
  </assembly>

  <!-- EXCLUDE EVERYTHING ELSE -->
  <!-- System.Drawing.Common - NOT NEEDED -->
  <!-- System.Windows.Forms - NOT NEEDED (WPF only) -->
  <!-- WinForms compatibility - NOT NEEDED -->
  <!-- Entity Framework - NOT NEEDED -->
  <!-- ASP.NET - NOT NEEDED -->
  <!-- WCF - NOT NEEDED -->
  <!-- Workflow - NOT NEEDED -->
  <!-- All graphics acceleration libraries - NOT NEEDED -->

  <!-- EXCLUDE INTEL GRAPHICS DRIVERS -->
  <assembly fullname="igd9dxva64" preserve="false" />
  <assembly fullname="igc64" preserve="false" />
  <assembly fullname="*intel*" preserve="false" />
  <assembly fullname="*graphics*" preserve="false" />
  <assembly fullname="*d3d*" preserve="false" />
  <assembly fullname="*directx*" preserve="false" />
</linker>
