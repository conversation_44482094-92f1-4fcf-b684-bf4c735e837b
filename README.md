# PowerVoice - Ultra Speech Transcription

PowerVoice is a .NET 9.0 WPF application that provides ultra speech transcription using Azure Speech Service with universal dictation capabilities. Type anywhere on your system using just your voice.

## Features

### 🎤 **Voice Transcription**
- Azure Speech Service integration for accurate transcription
- Real-time continuous speech recognition
- Support for interim and final results

### ⚡ **Universal Dictation**
- Type in any application using voice
- Send text to active windows automatically
- Support for both interim (real-time) and final results
- Smart word-based text tracking to prevent duplicates

### 🔍 **Global Keyboard Monitoring**
- Monitor all keyboard input across the system
- Real-time key press detection and logging
- Useful for debugging and system monitoring

### 📊 **Performance Analytics**
- Real-time latency tracking
- Session statistics (min, max, average latency)
- Comprehensive logging and performance metrics
- Copyable status information


## Technology Stack

- **.NET 9.0** - Latest .NET framework
- **WPF** - Windows Presentation Foundation UI  
- **Azure Speech Service** - Microsoft's cloud speech recognition
- **NAudio 2.2.1** - Professional audio processing
- **Microsoft.Extensions.Logging** - Comprehensive logging
- **Global Keyboard Hooks** - System-wide input monitoring

## Getting Started

### Prerequisites
- Windows 10/11
- .NET 9.0 Runtime
- Microphone access
- Valid Azure Speech Service subscription

### Setup
1. Clone the repository
2. Open `PowerVoice.sln` in Visual Studio 2022
3. Add your Azure Speech Service credentials to `appsettings.json`:
   ```json
   {
     "AzureSpeech": {
       "SubscriptionKey": "your-subscription-key-here",
       "Region": "your-region",
       "Endpoint": "https://your-region.api.cognitive.microsoft.com/"
     }
   }
   ```
4. Build and run the application

### Configuration
The application automatically configures optimal settings for transcription:
- English language detection (configurable)
- Real-time continuous recognition
- Smart word-based deduplication
- Professional audio format (16kHz, 16-bit, mono)

## How to Use

### Voice Transcription
1. Launch the application
2. The transcription will start automatically
3. Speak into your microphone
4. View real-time transcription in the app window

### Universal Dictation
1. Enable "Universal Dictation" checkbox
2. Optionally enable "Use Interim Results" for real-time typing
3. Click in any text field in any application
4. Speak - your voice will be converted to text in that application

### Keyboard Monitoring
- The application monitors all keyboard input system-wide
- Use "Clear Log" button to reset the keyboard log display
- Useful for debugging and system monitoring

## Architecture

### Speech Processing Pipeline
1. **Audio Capture** → NAudio microphone input
2. **Azure Speech Service** → Cloud-based speech recognition
3. **Ultra Processing** → Continuous recognition with interim results
4. **Universal Dictation** → Send transcribed text to any application
5. **Performance Tracking** → Latency measurement and analytics

### Key Components
- `AzureSpeechService` - Azure Speech SDK integration
- `UniversalDictation` - Cross-application text injection
- `GlobalKeyboardHook` - System-wide keyboard monitoring
- `MainWindow` - WPF UI with real-time updates
- `PerformanceLogger` - Comprehensive performance analytics

## Performance Optimizations

### Latency Reduction
- Direct integration with Azure Speech Service
- Optimized audio format (16kHz, 16-bit, mono)
- Smart word-based text deduplication
- Real-time interim result processing

### Quality Enhancements
- Professional audio capture via NAudio
- Continuous speech recognition
- Global keyboard hook for system monitoring
- Enhanced session tracking and analytics

## Contributing

1. Fork the repository
2. Create a feature branch
3. Follow the established coding patterns
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Create an issue in the GitHub repository
- Check the comprehensive logging output
- Review the Azure Speech Service documentation

---

Built with ❤️ using .NET 9.0 and Azure Speech Service
